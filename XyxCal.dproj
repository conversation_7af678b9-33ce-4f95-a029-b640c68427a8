﻿<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
    <PropertyGroup>
        <ProjectGuid>{7DB1D9E7-9219-4574-A409-6D962652EF59}</ProjectGuid>
        <ProjectVersion>15.1</ProjectVersion>
        <FrameworkType>VCL</FrameworkType>
        <MainSource>XyxCal.dpr</MainSource>
        <Base>True</Base>
        <Config Condition="'$(Config)'==''">Debug</Config>
        <Platform Condition="'$(Platform)'==''">Win32</Platform>
        <TargetedPlatforms>1</TargetedPlatforms>
        <AppType>Application</AppType>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Base' or '$(Base)'!=''">
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Base)'=='true') or '$(Base_Win32)'!=''">
        <Base_Win32>true</Base_Win32>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win64' and '$(Base)'=='true') or '$(Base_Win64)'!=''">
        <Base_Win64>true</Base_Win64>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Debug' or '$(Cfg_1)'!=''">
        <Cfg_1>true</Cfg_1>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Cfg_1)'=='true') or '$(Cfg_1_Win32)'!=''">
        <Cfg_1_Win32>true</Cfg_1_Win32>
        <CfgParent>Cfg_1</CfgParent>
        <Cfg_1>true</Cfg_1>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Release' or '$(Cfg_2)'!=''">
        <Cfg_2>true</Cfg_2>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base)'!=''">
        <Icon_MainIcon>$(BDS)\bin\delphi_PROJECTICON.ico</Icon_MainIcon>
        <DCC_Namespace>System;Xml;Data;Datasnap;Web;Soap;Vcl;Vcl.Imaging;Vcl.Touch;Vcl.Samples;Vcl.Shell;$(DCC_Namespace)</DCC_Namespace>
        <DCC_DcuOutput>.\$(Platform)\$(Config)</DCC_DcuOutput>
        <DCC_ExeOutput>.\$(Platform)\$(Config)</DCC_ExeOutput>
        <DCC_E>false</DCC_E>
        <DCC_N>false</DCC_N>
        <DCC_S>false</DCC_S>
        <DCC_F>false</DCC_F>
        <DCC_K>false</DCC_K>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base_Win32)'!=''">
        <DCC_UsePackage>HtImageH;tmsdXE5;autoupgrXE5;myprovider190;adsprovider190;FireDACSqliteDriver;RtmRxDB190;DBXSqliteDriver;FireDACPgDriver;fmx;RaizeComponentsVcl;IndySystem;esbpcs_dates_db_rt_d19;tmswizdXE5;frx19;dacvcl190;vclib;inetdbbde;DBXInterBaseDriver;DataSnapClient;DataSnapCommon;DataSnapServer;RtmRxBDE190;Tee919;DataSnapProviderClient;TeeUI919;DBXSybaseASEDriver;acntDelphiXE5_R;DbxCommonDriver;vclimg;unidac190;dbxcds;DatasnapConnectorsFreePascal;MetropolisUILiveTile;TeeGL919;vcldb;vcldsnap;fmxFireDAC;DBXDb2Driver;accessprovider190;EmbeddedWebBrowser;DBXOracleDriver;CustomIPTransport;unidacfmx190;vclribbon;TeeWorld919;dsnap;IndyIPServer;fmxase;vcl;IndyCore;liteprovider190;IndyIPCommon;CloudService;DBXMSSQLDriver;CodeSiteExpressPkg;FireDACIBDriver;esbpcs_base_db_rt_d19;esbpcs_stats_db_rt_d19;crcontrols190;db2provider190;FireDACDBXDriver;Package1;TeePro919;inetdbxpress;frxe19;FireDACDb2Driver;adortl;esbpcs_base_rt_d19;frxDB19;FireDACASADriver;esbpcs_calcs_db_rt_d19;dacfmx190;esbpcs_stats_rt_d19;ibprovider190;bindcompfmx;vcldbx;RaizeComponentsVclDb;FireDACODBCDriver;XLSRWII4;rtl;dbrtl;DbxClientDriver;FireDACCommon;bindcomp;inetdb;dbfprovider190;imports;DBXOdbcDriver;odbcprovider190;vclFireDAC;xmlrtl;svnui;ibxpress;aseprovider190;IndyProtocols;DBXMySQLDriver;RtmRxCtl190;esbpcs_calcs_rt_d19;FireDACCommonDriver;esbpcs_dates_rt_d19;tmsxlsdXE5;bindengine;vclactnband;soaprtl;bindcompdbx;TeeLanguage919;bindcompvcl;vclie;FireDACADSDriver;vcltouch;PngComponents;dac190;VclSmp;FireDACMSSQLDriver;FireDAC;VCLRESTComponents;Intraweb;DBXInformixDriver;ZipMasterR;DataSnapConnectors;FireDACDataSnapDriver;oraprovider190;unidacvcl190;dsnapcon;DBXFirebirdDriver;inet;pgprovider190;fmxobj;FireDACMySQLDriver;vclx;svn;DBXSybaseASADriver;tmsexdXE5;FireDACOracleDriver;fmxdae;RESTComponents;TeeDB919;bdertl;TeeImage919;FireDACMSAccDriver;DataSnapIndy10ServerTransport;dbexpress;IndyIPClient;$(DCC_UsePackage)</DCC_UsePackage>
        <VerInfo_Locale>1033</VerInfo_Locale>
        <VerInfo_IncludeVerInfo>true</VerInfo_IncludeVerInfo>
        <VerInfo_Keys>CompanyName=;FileDescription=;FileVersion=*******;InternalName=;LegalCopyright=;LegalTrademarks=;OriginalFilename=;ProductName=;ProductVersion=*******;Comments=</VerInfo_Keys>
        <DCC_Namespace>Winapi;System.Win;Data.Win;Datasnap.Win;Web.Win;Soap.Win;Xml.Win;Bde;$(DCC_Namespace)</DCC_Namespace>
        <Manifest_File>$(BDS)\bin\default_app.manifest</Manifest_File>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base_Win64)'!=''">
        <DCC_UsePackage>tmsdXE5;FireDACSqliteDriver;DBXSqliteDriver;FireDACPgDriver;fmx;RaizeComponentsVcl;IndySystem;dacvcl190;vclib;DBXInterBaseDriver;DataSnapClient;DataSnapCommon;DataSnapServer;DataSnapProviderClient;DBXSybaseASEDriver;acntDelphiXE5_R;DbxCommonDriver;vclimg;unidac190;dbxcds;DatasnapConnectorsFreePascal;MetropolisUILiveTile;vcldb;vcldsnap;fmxFireDAC;DBXDb2Driver;DBXOracleDriver;CustomIPTransport;unidacfmx190;vclribbon;dsnap;IndyIPServer;fmxase;vcl;IndyCore;liteprovider190;IndyIPCommon;CloudService;DBXMSSQLDriver;FireDACIBDriver;crcontrols190;FireDACDBXDriver;inetdbxpress;FireDACDb2Driver;adortl;FireDACASADriver;dacfmx190;bindcompfmx;RaizeComponentsVclDb;FireDACODBCDriver;rtl;dbrtl;DbxClientDriver;FireDACCommon;bindcomp;inetdb;DBXOdbcDriver;vclFireDAC;xmlrtl;ibxpress;IndyProtocols;DBXMySQLDriver;FireDACCommonDriver;tmsxlsdXE5;bindengine;vclactnband;soaprtl;bindcompdbx;bindcompvcl;vclie;FireDACADSDriver;vcltouch;PngComponents;dac190;VclSmp;FireDACMSSQLDriver;FireDAC;VCLRESTComponents;Intraweb;DBXInformixDriver;ZipMasterR;DataSnapConnectors;FireDACDataSnapDriver;unidacvcl190;dsnapcon;DBXFirebirdDriver;inet;pgprovider190;fmxobj;FireDACMySQLDriver;vclx;DBXSybaseASADriver;tmsexdXE5;FireDACOracleDriver;fmxdae;RESTComponents;FireDACMSAccDriver;DataSnapIndy10ServerTransport;dbexpress;IndyIPClient;$(DCC_UsePackage)</DCC_UsePackage>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_1)'!=''">
        <DCC_Define>DEBUG;$(DCC_Define)</DCC_Define>
        <DCC_DebugDCUs>true</DCC_DebugDCUs>
        <DCC_Optimize>false</DCC_Optimize>
        <DCC_GenerateStackFrames>true</DCC_GenerateStackFrames>
        <DCC_DebugInfoInExe>true</DCC_DebugInfoInExe>
        <DCC_RemoteDebug>true</DCC_RemoteDebug>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_1_Win32)'!=''">
        <VerInfo_IncludeVerInfo>true</VerInfo_IncludeVerInfo>
        <VerInfo_Locale>1033</VerInfo_Locale>
        <DCC_RemoteDebug>false</DCC_RemoteDebug>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_2)'!=''">
        <DCC_LocalDebugSymbols>false</DCC_LocalDebugSymbols>
        <DCC_Define>RELEASE;$(DCC_Define)</DCC_Define>
        <DCC_SymbolReferenceInfo>0</DCC_SymbolReferenceInfo>
        <DCC_DebugInformation>0</DCC_DebugInformation>
    </PropertyGroup>
    <ItemGroup>
        <DelphiCompile Include="$(MainSource)">
            <MainSource>MainSource</MainSource>
        </DelphiCompile>
        <DCCReference Include="facade\Cc_LoadingCloseFrm.pas">
            <Form>LoadingCloseForm</Form>
            <FormType>dfm</FormType>
        </DCCReference>
        <DCCReference Include="facade\Cc_LoadingFrm.pas">
            <Form>LoadingForm</Form>
            <FormType>dfm</FormType>
        </DCCReference>
        <DCCReference Include="facade\Cc_LoadingPicFrm.pas">
            <Form>LoadingPicForm</Form>
            <FormType>dfm</FormType>
        </DCCReference>
        <DCCReference Include="facade\Cc_LoadingSaveFpdFrm.pas">
            <Form>LoadingSaveFpdForm</Form>
            <FormType>dfm</FormType>
        </DCCReference>
        <DCCReference Include="facade\Cc_LoadingSaveFrm.pas">
            <Form>LoadingSaveForm</Form>
            <FormType>dfm</FormType>
        </DCCReference>
        <DCCReference Include="facade\Cc_LoadingSmallFrm.pas">
            <Form>LoadingSmallForm</Form>
            <FormType>dfm</FormType>
        </DCCReference>
        <DCCReference Include="facade\Cc_LoadingTabFrm.pas">
            <Form>LoadingTabForm</Form>
            <FormType>dfm</FormType>
        </DCCReference>
        <DCCReference Include="facade\Cc_OrdersPljsFrm.pas">
            <Form>Cc_OrdersPljsForm</Form>
            <FormType>dfm</FormType>
        </DCCReference>
        <DCCReference Include="facade\CC_YlCalFrm.pas">
            <Form>CalForm</Form>
            <FormType>dfm</FormType>
        </DCCReference>
        <DCCReference Include="common\CcMaterial.pas"/>
        <DCCReference Include="common\CcMaterialDetail.pas"/>
        <DCCReference Include="common\CcMaterialFz.pas"/>
        <DCCReference Include="common\CcSjScjd.pas"/>
        <DCCReference Include="common\CommonUtil.pas"/>
        <DCCReference Include="common\DMUtil.pas">
            <Form>DataModule1</Form>
            <FormType>dfm</FormType>
            <DesignClass>TDataModule</DesignClass>
        </DCCReference>
        <DCCReference Include="common\KsMaterialRecord.pas"/>
        <DCCReference Include="common\LoadingCloseThread.pas"/>
        <DCCReference Include="common\LoadingPicThread.pas"/>
        <DCCReference Include="common\LoadingSaveFpdThread.pas"/>
        <DCCReference Include="common\LoadingSaveThread.pas"/>
        <DCCReference Include="common\LoadingSmallThread.pas"/>
        <DCCReference Include="common\LoadingTabThread.pas"/>
        <DCCReference Include="common\LoadingThread.pas"/>
        <DCCReference Include="common\ThreadPdCal.pas"/>
        <DCCReference Include="common\ThreadPdMoreKsCal.pas"/>
        <DCCReference Include="common\XxDd.pas"/>
        <DCCReference Include="common\ThreadPdMoreKsCalBig.pas"/>
        <DCCReference Include="common\ThreadPdCalBig.pas"/>
        <BuildConfiguration Include="Release">
            <Key>Cfg_2</Key>
            <CfgParent>Base</CfgParent>
        </BuildConfiguration>
        <BuildConfiguration Include="Base">
            <Key>Base</Key>
        </BuildConfiguration>
        <BuildConfiguration Include="Debug">
            <Key>Cfg_1</Key>
            <CfgParent>Base</CfgParent>
        </BuildConfiguration>
    </ItemGroup>
    <ProjectExtensions>
        <Borland.Personality>Delphi.Personality.12</Borland.Personality>
        <Borland.ProjectType/>
        <BorlandProject>
            <Delphi.Personality>
                <VersionInfo>
                    <VersionInfo Name="IncludeVerInfo">False</VersionInfo>
                    <VersionInfo Name="AutoIncBuild">False</VersionInfo>
                    <VersionInfo Name="MajorVer">1</VersionInfo>
                    <VersionInfo Name="MinorVer">0</VersionInfo>
                    <VersionInfo Name="Release">0</VersionInfo>
                    <VersionInfo Name="Build">0</VersionInfo>
                    <VersionInfo Name="Debug">False</VersionInfo>
                    <VersionInfo Name="PreRelease">False</VersionInfo>
                    <VersionInfo Name="Special">False</VersionInfo>
                    <VersionInfo Name="Private">False</VersionInfo>
                    <VersionInfo Name="DLL">False</VersionInfo>
                    <VersionInfo Name="Locale">2052</VersionInfo>
                    <VersionInfo Name="CodePage">936</VersionInfo>
                </VersionInfo>
                <VersionInfoKeys>
                    <VersionInfoKeys Name="CompanyName"/>
                    <VersionInfoKeys Name="FileDescription"/>
                    <VersionInfoKeys Name="FileVersion">*******</VersionInfoKeys>
                    <VersionInfoKeys Name="InternalName"/>
                    <VersionInfoKeys Name="LegalCopyright"/>
                    <VersionInfoKeys Name="LegalTrademarks"/>
                    <VersionInfoKeys Name="OriginalFilename"/>
                    <VersionInfoKeys Name="ProductName"/>
                    <VersionInfoKeys Name="ProductVersion">*******</VersionInfoKeys>
                    <VersionInfoKeys Name="Comments"/>
                    <VersionInfoKeys Name="CFBundleName"/>
                    <VersionInfoKeys Name="CFBundleDisplayName"/>
                    <VersionInfoKeys Name="UIDeviceFamily"/>
                    <VersionInfoKeys Name="CFBundleIdentifier"/>
                    <VersionInfoKeys Name="CFBundleVersion"/>
                    <VersionInfoKeys Name="CFBundlePackageType"/>
                    <VersionInfoKeys Name="CFBundleSignature"/>
                    <VersionInfoKeys Name="CFBundleAllowMixedLocalizations"/>
                    <VersionInfoKeys Name="UISupportedInterfaceOrientations"/>
                    <VersionInfoKeys Name="CFBundleExecutable"/>
                    <VersionInfoKeys Name="CFBundleResourceSpecification"/>
                    <VersionInfoKeys Name="LSRequiresIPhoneOS"/>
                    <VersionInfoKeys Name="CFBundleInfoDictionaryVersion"/>
                    <VersionInfoKeys Name="CFBundleDevelopmentRegion"/>
                    <VersionInfoKeys Name="package"/>
                    <VersionInfoKeys Name="label"/>
                    <VersionInfoKeys Name="versionCode"/>
                    <VersionInfoKeys Name="versionName"/>
                    <VersionInfoKeys Name="persistent"/>
                    <VersionInfoKeys Name="restoreAnyVersion"/>
                    <VersionInfoKeys Name="installLocation"/>
                    <VersionInfoKeys Name="largeHeap"/>
                    <VersionInfoKeys Name="theme"/>
                </VersionInfoKeys>
                <Source>
                    <Source Name="MainSource">XyxCal.dpr</Source>
                </Source>
                <Excluded_Packages>
                    <Excluded_Packages Name="C:\Users\<USER>\Documents\RAD Studio\12.0\Bpl\tmsdedXE5.bpl">TMS Component Pack design time support</Excluded_Packages>
                </Excluded_Packages>
            </Delphi.Personality>
            <Deployment/>
            <Platforms>
                <Platform value="Win32">True</Platform>
                <Platform value="Win64">False</Platform>
            </Platforms>
        </BorlandProject>
        <ProjectFileVersion>12</ProjectFileVersion>
    </ProjectExtensions>
    <Import Project="$(BDS)\Bin\CodeGear.Delphi.Targets" Condition="Exists('$(BDS)\Bin\CodeGear.Delphi.Targets')"/>
    <Import Project="$(APPDATA)\Embarcadero\$(BDSAPPDATABASEDIR)\$(PRODUCTVERSION)\UserTools.proj" Condition="Exists('$(APPDATA)\Embarcadero\$(BDSAPPDATABASEDIR)\$(PRODUCTVERSION)\UserTools.proj')"/>
</Project>
