unit KsMaterialRecord;

interface

uses
  Classes;

type
  TKsMaterialRecord = class
  private
    FKsmaterialrecordid: integer;
    FKsid: integer;
    FKh: string;
    FKsbm: string;
    FLb: string;
    FMl: string;
    FKs: string;
    FKz: string;
    FYs: string;
    FItem_1_1_1: string;
    FItem_1_1_2: string;
    FItem_1_1_3: string;
    FItem_1_1_4: string;
    FItem_1_1_5: integer;
    FItem_1_1_6: integer;
    FItem_1_1_7: double;
    FItem_1_1_8: double;
    FItem_1_1_9: double;
    FItem_1_1_10: double;
    FItem_1_1_11: double;
    FItem_1_1_12: double;
    FItem_1_1_13: double;
    FItem_1_1_14: double;
    FItem_1_1_15: integer;
    FItem_1_1_16: integer;
    FItem_1_1_17: integer;
    FItem_1_1_18: integer;
    FItem_1_1_19: string;
    FItem_1_1_20: string;
    FItem_1_2_1: string;
    FItem_1_2_2: string;
    FItem_1_2_3: string;
    FItem_1_2_4: string;
    FItem_1_2_5: integer;
    FItem_1_2_6: integer;
    FItem_1_2_7: double;
    FItem_1_2_8: double;
    FItem_1_2_9: double;
    FItem_1_2_10: double;
    FItem_1_2_11: double;
    FItem_1_2_12: double;
    FItem_1_2_13: double;
    FItem_1_2_14: double;
    FItem_1_2_15: integer;
    FItem_1_2_16: integer;
    FItem_1_2_17: integer;
    FItem_1_2_18: integer;
    FItem_1_2_19: string;
    FItem_1_2_20: string;

    FItem_1_3_1: string;
    FItem_1_3_2: string;
    FItem_1_3_3: string;
    FItem_1_3_4: string;
    FItem_1_3_5: integer;
    FItem_1_3_6: integer;
    FItem_1_3_7: double;
    FItem_1_3_8: double;
    FItem_1_3_9: double;
    FItem_1_3_10: double;
    FItem_1_3_11: double;
    FItem_1_3_12: double;
    FItem_1_3_13: double;
    FItem_1_3_14: double;
    FItem_1_3_15: integer;
    FItem_1_3_16: integer;
    FItem_1_3_17: integer;
    FItem_1_3_18: integer;
    FItem_1_3_19: string;
    FItem_1_3_20: string;

    FItem_1_4_1: string;
    FItem_1_4_2: string;
    FItem_1_4_3: string;
    FItem_1_4_4: string;
    FItem_1_4_5: integer;
    FItem_1_4_6: integer;
    FItem_1_4_7: double;
    FItem_1_4_8: double;
    FItem_1_4_9: double;
    FItem_1_4_10: double;
    FItem_1_4_11: double;
    FItem_1_4_12: integer;
    FItem_1_4_13: integer;
    FItem_1_4_14: integer;
    FItem_1_4_15: integer;
    FItem_1_4_16: string;
    FItem_1_4_17: string;

    FItem_1_5_1: string;
    FItem_1_5_2: string;
    FItem_1_5_3: string;
    FItem_1_5_4: string;
    FItem_1_5_5: integer;
    FItem_1_5_6: integer;
    FItem_1_5_7: double;
    FItem_1_5_8: double;
    FItem_1_5_9: double;
    FItem_1_5_10: double;
    FItem_1_5_11: double;
    FItem_1_5_12: double;
    FItem_1_5_13: double;
    FItem_1_5_14: double;
    FItem_1_5_15: integer;
    FItem_1_5_16: integer;
    FItem_1_5_17: integer;
    FItem_1_5_18: integer;
    FItem_1_5_19: string;
    FItem_1_5_20: string;

    FItem_1_6_1: string;
    FItem_1_6_2: string;
    FItem_1_6_3: string;
    FItem_1_6_4: string;
    FItem_1_6_5: integer;
    FItem_1_6_6: integer;
    FItem_1_6_7: double;
    FItem_1_6_8: double;
    FItem_1_6_9: double;
    FItem_1_6_10: double;
    FItem_1_6_11: double;
    FItem_1_6_12: integer;
    FItem_1_6_13: integer;
    FItem_1_6_14: integer;
    FItem_1_6_15: integer;
    FItem_1_6_16: string;
    FItem_1_6_17: string;

    FItem_2_1_1: string;
    FItem_2_1_2: string;
    FItem_2_1_3: string;
    FItem_2_1_4: string;
    FItem_2_1_5: integer;
    FItem_2_1_6: integer;
    FItem_2_1_7: double;
    FItem_2_1_8: double;
    FItem_2_1_9: double;
    FItem_2_1_10: double;
    FItem_2_1_11: double;
    FItem_2_1_12: double;
    FItem_2_1_13: double;
    FItem_2_1_14: double;
    FItem_2_1_15: double;
    FItem_2_1_16: integer;
    FItem_2_1_17: integer;
    FItem_2_1_18: integer;
    FItem_2_1_19: integer;
    FItem_2_1_20: string;
    FItem_2_1_21: string;
    FItem_2_2_1: string;
    FItem_2_2_2: string;
    FItem_2_2_3: string;
    FItem_2_2_4: string;
    FItem_2_2_5: integer;
    FItem_2_2_6: integer;
    FItem_2_2_7: double;
    FItem_2_2_8: double;
    FItem_2_2_9: double;
    FItem_2_2_10: double;
    FItem_2_2_11: double;
    FItem_2_2_12: double;
    FItem_2_2_13: double;
    FItem_2_2_14: double;
    FItem_2_2_15: integer;
    FItem_2_2_16: integer;
    FItem_2_2_17: integer;
    FItem_2_2_18: integer;
    FItem_2_2_19: string;
    FItem_2_2_20: string;

    FItem_2_3_1: string;
    FItem_2_3_2: string;
    FItem_2_3_3: string;
    FItem_2_3_4: string;
    FItem_2_3_5: integer;
    FItem_2_3_6: integer;
    FItem_2_3_7: double;
    FItem_2_3_8: double;
    FItem_2_3_9: double;
    FItem_2_3_10: double;
    FItem_2_3_11: double;
    FItem_2_3_12: double;
    FItem_2_3_13: integer;
    FItem_2_3_14: integer;
    FItem_2_3_15: integer;
    FItem_2_3_16: integer;
    FItem_2_3_17: string;
    FItem_2_3_18: string;

    FItem_2_4_1: string;
    FItem_2_4_2: string;
    FItem_2_4_3: string;
    FItem_2_4_4: string;
    FItem_2_4_5: integer;
    FItem_2_4_6: integer;
    FItem_2_4_7: double;
    FItem_2_4_8: double;
    FItem_2_4_9: double;
    FItem_2_4_10: double;
    FItem_2_4_11: double;
    FItem_2_4_12: double;
    FItem_2_4_13: integer;
    FItem_2_4_14: integer;
    FItem_2_4_15: integer;
    FItem_2_4_16: integer;
    FItem_2_4_17: string;
    FItem_2_4_18: string;

    FItem_3_1_1: string;
    FItem_3_1_2: string;
    FItem_3_1_3: string;
    FItem_3_1_4: string;
    FItem_3_1_5: integer;
    FItem_3_1_6: integer;
    FItem_3_1_7: double;
    FItem_3_1_8: double;
    FItem_3_1_9: double;
    FItem_3_1_10: double;
    FItem_3_1_11: double;
    FItem_3_1_12: integer;
    FItem_3_1_13: integer;
    FItem_3_1_14: integer;
    FItem_3_1_15: integer;
    FItem_3_1_16: string;
    FItem_3_1_17: string;

    FItem_3_2_1: string;
    FItem_3_2_2: string;
    FItem_3_2_3: string;
    FItem_3_2_4: string;
    FItem_3_2_5: integer;
    FItem_3_2_6: integer;
    FItem_3_2_7: double;
    FItem_3_2_8: double;
    FItem_3_2_9: double;
    FItem_3_2_10: double;
    FItem_3_2_11: double;
    FItem_3_2_12: double;
    FItem_3_2_13: double;
    FItem_3_2_14: double;
    FItem_3_2_15: double;
    FItem_3_2_16: integer;
    FItem_3_2_17: integer;
    FItem_3_2_18: integer;
    FItem_3_2_19: integer;
    FItem_3_2_20: string;
    FItem_3_2_21: string;

    FItem_4_1_1: string;
    FItem_4_1_2: string;
    FItem_4_1_3: string;
    FItem_4_1_4: string;
    FItem_4_1_5: integer;
    FItem_4_1_6: integer;
    FItem_4_1_7: double;
    FItem_4_1_8: double;
    FItem_4_1_9: double;
    FItem_4_1_10: double;
    FItem_4_1_11: double;
    FItem_4_1_12: double;
    FItem_4_1_13: double;
    FItem_4_1_14: double;
    FItem_4_1_15: integer;
    FItem_4_1_16: integer;
    FItem_4_1_17: integer;
    FItem_4_1_18: integer;
    FItem_4_1_19: string;
    FItem_4_1_20: string;

    FItem_4_2_1: string;
    FItem_4_2_2: string;
    FItem_4_2_3: string;
    FItem_4_2_4: string;
    FItem_4_2_5: integer;
    FItem_4_2_6: integer;
    FItem_4_2_7: double;
    FItem_4_2_8: double;
    FItem_4_2_9: double;
    FItem_4_2_10: double;
    FItem_4_2_11: double;
    FItem_4_2_12: double;
    FItem_4_2_13: double;
    FItem_4_2_14: double;
    FItem_4_2_15: integer;
    FItem_4_2_16: integer;
    FItem_4_2_17: integer;
    FItem_4_2_18: integer;
    FItem_4_2_19: string;
    FItem_4_2_20: string;

    FItem_5_1_1: string;
    FItem_5_1_2: string;
    FItem_5_1_3: string;
    FItem_5_1_4: string;
    FItem_5_1_5: integer;
    FItem_5_1_6: integer;
    FItem_5_1_7: double;
    FItem_5_1_8: double;
    FItem_5_1_9: double;
    FItem_5_1_10: double;
    FItem_5_1_11: double;
    FItem_5_1_12: double;
    FItem_5_1_13: double;
    FItem_5_1_14: double;
    FItem_5_1_15: double;
    FItem_5_1_16: double;
    FItem_5_1_17: integer;
    FItem_5_1_18: integer;
    FItem_5_1_19: integer;
    FItem_5_1_20: integer;
    FItem_5_1_21: string;
    FItem_5_1_22: string;

    FItem_5_2_1: string;
    FItem_5_2_2: string;
    FItem_5_2_3: string;
    FItem_5_2_4: string;
    FItem_5_2_5: integer;
    FItem_5_2_6: integer;
    FItem_5_2_7: double;
    FItem_5_2_8: double;
    FItem_5_2_9: double;
    FItem_5_2_10: double;
    FItem_5_2_11: double;
    FItem_5_2_12: double;
    FItem_5_2_13: double;
    FItem_5_2_14: double;
    FItem_5_2_15: double;
    FItem_5_2_16: double;
    FItem_5_2_17: double;
    FItem_5_2_18: integer;
    FItem_5_2_19: integer;
    FItem_5_2_20: integer;
    FItem_5_2_21: integer;
    FItem_5_2_22: string;
    FItem_5_2_23: string;

    FMarketTime: string;
    FMarketUser: string;
    FState: string;

  public
    property Ksmaterialrecordid: integer read FKsmaterialrecordid
      write FKsmaterialrecordid;
    property Ksid: integer read FKsid write FKsid;
    property Kh: string read FKh write FKh;
    property Ksbm: string read FKsbm write FKsbm;
    property Lb: string read FLb write FLb;
    property Ml: string read FMl write FMl;
    property Ks: string read FKs write FKs;
    property Kz: string read FKz write FKz;
    property Ys: string read FYs write FYs;

    property Item_1_1_1: string read FItem_1_1_1 write FItem_1_1_1;
    property Item_1_1_2: string read FItem_1_1_2 write FItem_1_1_2;
    property Item_1_1_3: string read FItem_1_1_3 write FItem_1_1_3;
    property Item_1_1_4: string read FItem_1_1_4 write FItem_1_1_4;
    property Item_1_1_5: integer read FItem_1_1_5 write FItem_1_1_5;
    property Item_1_1_6: integer read FItem_1_1_6 write FItem_1_1_6;
    property Item_1_1_7: double read FItem_1_1_7 write FItem_1_1_7;
    property Item_1_1_8: double read FItem_1_1_8 write FItem_1_1_8;
    property Item_1_1_9: double read FItem_1_1_9 write FItem_1_1_9;
    property Item_1_1_10: double read FItem_1_1_10 write FItem_1_1_10;
    property Item_1_1_11: double read FItem_1_1_11 write FItem_1_1_11;
    property Item_1_1_12: double read FItem_1_1_12 write FItem_1_1_12;
    property Item_1_1_13: double read FItem_1_1_13 write FItem_1_1_13;
    property Item_1_1_14: double read FItem_1_1_14 write FItem_1_1_14;
    property Item_1_1_15: integer read FItem_1_1_15 write FItem_1_1_15;
    property Item_1_1_16: integer read FItem_1_1_16 write FItem_1_1_16;
    property Item_1_1_17: integer read FItem_1_1_17 write FItem_1_1_17;
    property Item_1_1_18: integer read FItem_1_1_18 write FItem_1_1_18;
    property Item_1_1_19: string read FItem_1_1_19 write FItem_1_1_19;
    property Item_1_1_20: string read FItem_1_1_20 write FItem_1_1_20;

    property Item_1_2_1: string read FItem_1_2_1 write FItem_1_2_1;
    property Item_1_2_2: string read FItem_1_2_2 write FItem_1_2_2;
    property Item_1_2_3: string read FItem_1_2_3 write FItem_1_2_3;
    property Item_1_2_4: string read FItem_1_2_4 write FItem_1_2_4;
    property Item_1_2_5: integer read FItem_1_2_5 write FItem_1_2_5;
    property Item_1_2_6: integer read FItem_1_2_6 write FItem_1_2_6;
    property Item_1_2_7: double read FItem_1_2_7 write FItem_1_2_7;
    property Item_1_2_8: double read FItem_1_2_8 write FItem_1_2_8;
    property Item_1_2_9: double read FItem_1_2_9 write FItem_1_2_9;
    property Item_1_2_10: double read FItem_1_2_10 write FItem_1_2_10;
    property Item_1_2_11: double read FItem_1_2_11 write FItem_1_2_11;
    property Item_1_2_12: double read FItem_1_2_12 write FItem_1_2_12;
    property Item_1_2_13: double read FItem_1_2_13 write FItem_1_2_13;
    property Item_1_2_14: double read FItem_1_2_14 write FItem_1_2_14;
    property Item_1_2_15: integer read FItem_1_2_15 write FItem_1_2_15;
    property Item_1_2_16: integer read FItem_1_2_16 write FItem_1_2_16;
    property Item_1_2_17: integer read FItem_1_2_17 write FItem_1_2_17;
    property Item_1_2_18: integer read FItem_1_2_18 write FItem_1_2_18;
    property Item_1_2_19: string read FItem_1_2_19 write FItem_1_2_19;
    property Item_1_2_20: string read FItem_1_2_20 write FItem_1_2_20;

    property Item_1_3_1: string read FItem_1_3_1 write FItem_1_3_1;
    property Item_1_3_2: string read FItem_1_3_2 write FItem_1_3_2;
    property Item_1_3_3: string read FItem_1_3_3 write FItem_1_3_3;
    property Item_1_3_4: string read FItem_1_3_4 write FItem_1_3_4;
    property Item_1_3_5: integer read FItem_1_3_5 write FItem_1_3_5;
    property Item_1_3_6: integer read FItem_1_3_6 write FItem_1_3_6;
    property Item_1_3_7: double read FItem_1_3_7 write FItem_1_3_7;
    property Item_1_3_8: double read FItem_1_3_8 write FItem_1_3_8;
    property Item_1_3_9: double read FItem_1_3_9 write FItem_1_3_9;
    property Item_1_3_10: double read FItem_1_3_10 write FItem_1_3_10;
    property Item_1_3_11: double read FItem_1_3_11 write FItem_1_3_11;
    property Item_1_3_12: double read FItem_1_3_12 write FItem_1_3_12;
    property Item_1_3_13: double read FItem_1_3_13 write FItem_1_3_13;
    property Item_1_3_14: double read FItem_1_3_14 write FItem_1_3_14;
    property Item_1_3_15: integer read FItem_1_3_15 write FItem_1_3_15;
    property Item_1_3_16: integer read FItem_1_3_16 write FItem_1_3_16;
    property Item_1_3_17: integer read FItem_1_3_17 write FItem_1_3_17;
    property Item_1_3_18: integer read FItem_1_3_18 write FItem_1_3_18;
    property Item_1_3_19: string read FItem_1_3_19 write FItem_1_3_19;
    property Item_1_3_20: string read FItem_1_3_20 write FItem_1_3_20;

    property Item_1_4_1: string read FItem_1_4_1 write FItem_1_4_1;
    property Item_1_4_2: string read FItem_1_4_2 write FItem_1_4_2;
    property Item_1_4_3: string read FItem_1_4_3 write FItem_1_4_3;
    property Item_1_4_4: string read FItem_1_4_4 write FItem_1_4_4;
    property Item_1_4_5: integer read FItem_1_4_5 write FItem_1_4_5;
    property Item_1_4_6: integer read FItem_1_4_6 write FItem_1_4_6;
    property Item_1_4_7: double read FItem_1_4_7 write FItem_1_4_7;
    property Item_1_4_8: double read FItem_1_4_8 write FItem_1_4_8;
    property Item_1_4_9: double read FItem_1_4_9 write FItem_1_4_9;
    property Item_1_4_10: double read FItem_1_4_10 write FItem_1_4_10;
    property Item_1_4_11: double read FItem_1_4_11 write FItem_1_4_11;
    property Item_1_4_12: integer read FItem_1_4_12 write FItem_1_4_12;
    property Item_1_4_13: integer read FItem_1_4_13 write FItem_1_4_13;
    property Item_1_4_14: integer read FItem_1_4_14 write FItem_1_4_14;
    property Item_1_4_15: integer read FItem_1_4_15 write FItem_1_4_15;
    property Item_1_4_16: string read FItem_1_4_16 write FItem_1_4_16;
    property Item_1_4_17: string read FItem_1_4_17 write FItem_1_4_17;

    property Item_1_5_1: string read FItem_1_5_1 write FItem_1_5_1;
    property Item_1_5_2: string read FItem_1_5_2 write FItem_1_5_2;
    property Item_1_5_3: string read FItem_1_5_3 write FItem_1_5_3;
    property Item_1_5_4: string read FItem_1_5_4 write FItem_1_5_4;
    property Item_1_5_5: integer read FItem_1_5_5 write FItem_1_5_5;
    property Item_1_5_6: integer read FItem_1_5_6 write FItem_1_5_6;
    property Item_1_5_7: double read FItem_1_5_7 write FItem_1_5_7;
    property Item_1_5_8: double read FItem_1_5_8 write FItem_1_5_8;
    property Item_1_5_9: double read FItem_1_5_9 write FItem_1_5_9;
    property Item_1_5_10: double read FItem_1_5_10 write FItem_1_5_10;
    property Item_1_5_11: double read FItem_1_5_11 write FItem_1_5_11;
    property Item_1_5_12: double read FItem_1_5_12 write FItem_1_5_12;
    property Item_1_5_13: double read FItem_1_5_13 write FItem_1_5_13;
    property Item_1_5_14: double read FItem_1_5_14 write FItem_1_5_14;
    property Item_1_5_15: integer read FItem_1_5_15 write FItem_1_5_15;
    property Item_1_5_16: integer read FItem_1_5_16 write FItem_1_5_16;
    property Item_1_5_17: integer read FItem_1_5_17 write FItem_1_5_17;
    property Item_1_5_18: integer read FItem_1_5_18 write FItem_1_5_18;
    property Item_1_5_19: string read FItem_1_5_19 write FItem_1_5_19;
    property Item_1_5_20: string read FItem_1_5_20 write FItem_1_5_20;

    property Item_1_6_1: string read FItem_1_6_1 write FItem_1_6_1;
    property Item_1_6_2: string read FItem_1_6_2 write FItem_1_6_2;
    property Item_1_6_3: string read FItem_1_6_3 write FItem_1_6_3;
    property Item_1_6_4: string read FItem_1_6_4 write FItem_1_6_4;
    property Item_1_6_5: integer read FItem_1_6_5 write FItem_1_6_5;
    property Item_1_6_6: integer read FItem_1_6_6 write FItem_1_6_6;
    property Item_1_6_7: double read FItem_1_6_7 write FItem_1_6_7;
    property Item_1_6_8: double read FItem_1_6_8 write FItem_1_6_8;
    property Item_1_6_9: double read FItem_1_6_9 write FItem_1_6_9;
    property Item_1_6_10: double read FItem_1_6_10 write FItem_1_6_10;
    property Item_1_6_11: double read FItem_1_6_11 write FItem_1_6_11;
    property Item_1_6_12: integer read FItem_1_6_12 write FItem_1_6_12;
    property Item_1_6_13: integer read FItem_1_6_13 write FItem_1_6_13;
    property Item_1_6_14: integer read FItem_1_6_14 write FItem_1_6_14;
    property Item_1_6_15: integer read FItem_1_6_15 write FItem_1_6_15;
    property Item_1_6_16: string read FItem_1_6_16 write FItem_1_6_16;
    property Item_1_6_17: string read FItem_1_6_17 write FItem_1_6_17;

    property Item_2_1_1: string read FItem_2_1_1 write FItem_2_1_1;
    property Item_2_1_2: string read FItem_2_1_2 write FItem_2_1_2;
    property Item_2_1_3: string read FItem_2_1_3 write FItem_2_1_3;
    property Item_2_1_4: string read FItem_2_1_4 write FItem_2_1_4;
    property Item_2_1_5: integer read FItem_2_1_5 write FItem_2_1_5;
    property Item_2_1_6: integer read FItem_2_1_6 write FItem_2_1_6;
    property Item_2_1_7: double read FItem_2_1_7 write FItem_2_1_7;
    property Item_2_1_8: double read FItem_2_1_8 write FItem_2_1_8;
    property Item_2_1_9: double read FItem_2_1_9 write FItem_2_1_9;
    property Item_2_1_10: double read FItem_2_1_10 write FItem_2_1_10;
    property Item_2_1_11: double read FItem_2_1_11 write FItem_2_1_11;
    property Item_2_1_12: double read FItem_2_1_12 write FItem_2_1_12;
    property Item_2_1_13: double read FItem_2_1_13 write FItem_2_1_13;
    property Item_2_1_14: double read FItem_2_1_14 write FItem_2_1_14;
    property Item_2_1_15: double read FItem_2_1_15 write FItem_2_1_15;
    property Item_2_1_16: integer read FItem_2_1_16 write FItem_2_1_16;
    property Item_2_1_17: integer read FItem_2_1_17 write FItem_2_1_17;
    property Item_2_1_18: integer read FItem_2_1_18 write FItem_2_1_18;
    property Item_2_1_19: integer read FItem_2_1_19 write FItem_2_1_19;
    property Item_2_1_20: string read FItem_2_1_20 write FItem_2_1_20;
    property Item_2_1_21: string read FItem_2_1_21 write FItem_2_1_21;

    property Item_2_2_1: string read FItem_2_2_1 write FItem_2_2_1;
    property Item_2_2_2: string read FItem_2_2_2 write FItem_2_2_2;
    property Item_2_2_3: string read FItem_2_2_3 write FItem_2_2_3;
    property Item_2_2_4: string read FItem_2_2_4 write FItem_2_2_4;
    property Item_2_2_5: integer read FItem_2_2_5 write FItem_2_2_5;
    property Item_2_2_6: integer read FItem_2_2_6 write FItem_2_2_6;
    property Item_2_2_7: double read FItem_2_2_7 write FItem_2_2_7;
    property Item_2_2_8: double read FItem_2_2_8 write FItem_2_2_8;
    property Item_2_2_9: double read FItem_2_2_9 write FItem_2_2_9;
    property Item_2_2_10: double read FItem_2_2_10 write FItem_2_2_10;
    property Item_2_2_11: double read FItem_2_2_11 write FItem_2_2_11;
    property Item_2_2_12: double read FItem_2_2_12 write FItem_2_2_12;
    property Item_2_2_13: double read FItem_2_2_13 write FItem_2_2_13;
    property Item_2_2_14: double read FItem_2_2_14 write FItem_2_2_14;
    property Item_2_2_15: integer read FItem_2_2_15 write FItem_2_2_15;
    property Item_2_2_16: integer read FItem_2_2_16 write FItem_2_2_16;
    property Item_2_2_17: integer read FItem_2_2_17 write FItem_2_2_17;
    property Item_2_2_18: integer read FItem_2_2_18 write FItem_2_2_18;
    property Item_2_2_19: string read FItem_2_2_19 write FItem_2_2_19;
    property Item_2_2_20: string read FItem_2_2_20 write FItem_2_2_20;

    property Item_2_3_1: string read FItem_2_3_1 write FItem_2_3_1;
    property Item_2_3_2: string read FItem_2_3_2 write FItem_2_3_2;
    property Item_2_3_3: string read FItem_2_3_3 write FItem_2_3_3;
    property Item_2_3_4: string read FItem_2_3_4 write FItem_2_3_4;
    property Item_2_3_5: integer read FItem_2_3_5 write FItem_2_3_5;
    property Item_2_3_6: integer read FItem_2_3_6 write FItem_2_3_6;
    property Item_2_3_7: double read FItem_2_3_7 write FItem_2_3_7;
    property Item_2_3_8: double read FItem_2_3_8 write FItem_2_3_8;
    property Item_2_3_9: double read FItem_2_3_9 write FItem_2_3_9;
    property Item_2_3_10: double read FItem_2_3_10 write FItem_2_3_10;
    property Item_2_3_11: double read FItem_2_3_11 write FItem_2_3_11;
    property Item_2_3_12: double read FItem_2_3_12 write FItem_2_3_12;
    property Item_2_3_13: integer read FItem_2_3_13 write FItem_2_3_13;
    property Item_2_3_14: integer read FItem_2_3_14 write FItem_2_3_14;
    property Item_2_3_15: integer read FItem_2_3_15 write FItem_2_3_15;
    property Item_2_3_16: integer read FItem_2_3_16 write FItem_2_3_16;
    property Item_2_3_17: string read FItem_2_3_17 write FItem_2_3_17;
    property Item_2_3_18: string read FItem_2_3_18 write FItem_2_3_18;

    property Item_2_4_1: string read FItem_2_4_1 write FItem_2_4_1;
    property Item_2_4_2: string read FItem_2_4_2 write FItem_2_4_2;
    property Item_2_4_3: string read FItem_2_4_3 write FItem_2_4_3;
    property Item_2_4_4: string read FItem_2_4_4 write FItem_2_4_4;
    property Item_2_4_5: integer read FItem_2_4_5 write FItem_2_4_5;
    property Item_2_4_6: integer read FItem_2_4_6 write FItem_2_4_6;
    property Item_2_4_7: double read FItem_2_4_7 write FItem_2_4_7;
    property Item_2_4_8: double read FItem_2_4_8 write FItem_2_4_8;
    property Item_2_4_9: double read FItem_2_4_9 write FItem_2_4_9;
    property Item_2_4_10: double read FItem_2_4_10 write FItem_2_4_10;
    property Item_2_4_11: double read FItem_2_4_11 write FItem_2_4_11;
    property Item_2_4_12: double read FItem_2_4_12 write FItem_2_4_12;
    property Item_2_4_13: integer read FItem_2_4_13 write FItem_2_4_13;
    property Item_2_4_14: integer read FItem_2_4_14 write FItem_2_4_14;
    property Item_2_4_15: integer read FItem_2_4_15 write FItem_2_4_15;
    property Item_2_4_16: integer read FItem_2_4_16 write FItem_2_4_16;
    property Item_2_4_17: string read FItem_2_4_17 write FItem_2_4_17;
    property Item_2_4_18: string read FItem_2_4_18 write FItem_2_4_18;

    property Item_3_1_1: string read FItem_3_1_1 write FItem_3_1_1;
    property Item_3_1_2: string read FItem_3_1_2 write FItem_3_1_2;
    property Item_3_1_3: string read FItem_3_1_3 write FItem_3_1_3;
    property Item_3_1_4: string read FItem_3_1_4 write FItem_3_1_4;
    property Item_3_1_5: integer read FItem_3_1_5 write FItem_3_1_5;
    property Item_3_1_6: integer read FItem_3_1_6 write FItem_3_1_6;
    property Item_3_1_7: double read FItem_3_1_7 write FItem_3_1_7;
    property Item_3_1_8: double read FItem_3_1_8 write FItem_3_1_8;
    property Item_3_1_9: double read FItem_3_1_9 write FItem_3_1_9;
    property Item_3_1_10: double read FItem_3_1_10 write FItem_3_1_10;
    property Item_3_1_11: double read FItem_3_1_11 write FItem_3_1_11;
    property Item_3_1_12: integer read FItem_3_1_12 write FItem_3_1_12;
    property Item_3_1_13: integer read FItem_3_1_13 write FItem_3_1_13;
    property Item_3_1_14: integer read FItem_3_1_14 write FItem_3_1_14;
    property Item_3_1_15: integer read FItem_3_1_15 write FItem_3_1_15;
    property Item_3_1_16: string read FItem_3_1_16 write FItem_3_1_16;
    property Item_3_1_17: string read FItem_3_1_17 write FItem_3_1_17;

    property Item_3_2_1: string read FItem_3_2_1 write FItem_3_2_1;
    property Item_3_2_2: string read FItem_3_2_2 write FItem_3_2_2;
    property Item_3_2_3: string read FItem_3_2_3 write FItem_3_2_3;
    property Item_3_2_4: string read FItem_3_2_4 write FItem_3_2_4;
    property Item_3_2_5: integer read FItem_3_2_5 write FItem_3_2_5;
    property Item_3_2_6: integer read FItem_3_2_6 write FItem_3_2_6;
    property Item_3_2_7: double read FItem_3_2_7 write FItem_3_2_7;
    property Item_3_2_8: double read FItem_3_2_8 write FItem_3_2_8;
    property Item_3_2_9: double read FItem_3_2_9 write FItem_3_2_9;
    property Item_3_2_10: double read FItem_3_2_10 write FItem_3_2_10;
    property Item_3_2_11: double read FItem_3_2_11 write FItem_3_2_11;
    property Item_3_2_12: double read FItem_3_2_12 write FItem_3_2_12;
    property Item_3_2_13: double read FItem_3_2_13 write FItem_3_2_13;
    property Item_3_2_14: double read FItem_3_2_14 write FItem_3_2_14;
    property Item_3_2_15: double read FItem_3_2_15 write FItem_3_2_15;
    property Item_3_2_16: integer read FItem_3_2_16 write FItem_3_2_16;
    property Item_3_2_17: integer read FItem_3_2_17 write FItem_3_2_17;
    property Item_3_2_18: integer read FItem_3_2_18 write FItem_3_2_18;
    property Item_3_2_19: integer read FItem_3_2_19 write FItem_3_2_19;
    property Item_3_2_20: string read FItem_3_2_20 write FItem_3_2_20;
    property Item_3_2_21: string read FItem_3_2_21 write FItem_3_2_21;

    property Item_4_1_1: string read FItem_4_1_1 write FItem_4_1_1;
    property Item_4_1_2: string read FItem_4_1_2 write FItem_4_1_2;
    property Item_4_1_3: string read FItem_4_1_3 write FItem_4_1_3;
    property Item_4_1_4: string read FItem_4_1_4 write FItem_4_1_4;
    property Item_4_1_5: integer read FItem_4_1_5 write FItem_4_1_5;
    property Item_4_1_6: integer read FItem_4_1_6 write FItem_4_1_6;
    property Item_4_1_7: double read FItem_4_1_7 write FItem_4_1_7;
    property Item_4_1_8: double read FItem_4_1_8 write FItem_4_1_8;
    property Item_4_1_9: double read FItem_4_1_9 write FItem_4_1_9;
    property Item_4_1_10: double read FItem_4_1_10 write FItem_4_1_10;
    property Item_4_1_11: double read FItem_4_1_11 write FItem_4_1_11;
    property Item_4_1_12: double read FItem_4_1_12 write FItem_4_1_12;
    property Item_4_1_13: double read FItem_4_1_13 write FItem_4_1_13;
    property Item_4_1_14: double read FItem_4_1_14 write FItem_4_1_14;
    property Item_4_1_15: integer read FItem_4_1_15 write FItem_4_1_15;
    property Item_4_1_16: integer read FItem_4_1_16 write FItem_4_1_16;
    property Item_4_1_17: integer read FItem_4_1_17 write FItem_4_1_17;
    property Item_4_1_18: integer read FItem_4_1_18 write FItem_4_1_18;
    property Item_4_1_19: string read FItem_4_1_19 write FItem_4_1_19;
    property Item_4_1_20: string read FItem_4_1_20 write FItem_4_1_20;

    property Item_4_2_1: string read FItem_4_2_1 write FItem_4_2_1;
    property Item_4_2_2: string read FItem_4_2_2 write FItem_4_2_2;
    property Item_4_2_3: string read FItem_4_2_3 write FItem_4_2_3;
    property Item_4_2_4: string read FItem_4_2_4 write FItem_4_2_4;
    property Item_4_2_5: integer read FItem_4_2_5 write FItem_4_2_5;
    property Item_4_2_6: integer read FItem_4_2_6 write FItem_4_2_6;
    property Item_4_2_7: double read FItem_4_2_7 write FItem_4_2_7;
    property Item_4_2_8: double read FItem_4_2_8 write FItem_4_2_8;
    property Item_4_2_9: double read FItem_4_2_9 write FItem_4_2_9;
    property Item_4_2_10: double read FItem_4_2_10 write FItem_4_2_10;
    property Item_4_2_11: double read FItem_4_2_11 write FItem_4_2_11;
    property Item_4_2_12: double read FItem_4_2_12 write FItem_4_2_12;
    property Item_4_2_13: double read FItem_4_2_13 write FItem_4_2_13;
    property Item_4_2_14: double read FItem_4_2_14 write FItem_4_2_14;
    property Item_4_2_15: integer read FItem_4_2_15 write FItem_4_2_15;
    property Item_4_2_16: integer read FItem_4_2_16 write FItem_4_2_16;
    property Item_4_2_17: integer read FItem_4_2_17 write FItem_4_2_17;
    property Item_4_2_18: integer read FItem_4_2_18 write FItem_4_2_18;
    property Item_4_2_19: string read FItem_4_2_19 write FItem_4_2_19;
    property Item_4_2_20: string read FItem_4_2_20 write FItem_4_2_20;

    property Item_5_1_1: string read FItem_5_1_1 write FItem_5_1_1;
    property Item_5_1_2: string read FItem_5_1_2 write FItem_5_1_2;
    property Item_5_1_3: string read FItem_5_1_3 write FItem_5_1_3;
    property Item_5_1_4: string read FItem_5_1_4 write FItem_5_1_4;
    property Item_5_1_5: integer read FItem_5_1_5 write FItem_5_1_5;
    property Item_5_1_6: integer read FItem_5_1_6 write FItem_5_1_6;
    property Item_5_1_7: double read FItem_5_1_7 write FItem_5_1_7;
    property Item_5_1_8: double read FItem_5_1_8 write FItem_5_1_8;
    property Item_5_1_9: double read FItem_5_1_9 write FItem_5_1_9;
    property Item_5_1_10: double read FItem_5_1_10 write FItem_5_1_10;
    property Item_5_1_11: double read FItem_5_1_11 write FItem_5_1_11;
    property Item_5_1_12: double read FItem_5_1_12 write FItem_5_1_12;
    property Item_5_1_13: double read FItem_5_1_13 write FItem_5_1_13;
    property Item_5_1_14: double read FItem_5_1_14 write FItem_5_1_14;
    property Item_5_1_15: double read FItem_5_1_15 write FItem_5_1_15;
    property Item_5_1_16: double read FItem_5_1_16 write FItem_5_1_16;
    property Item_5_1_17: integer read FItem_5_1_17 write FItem_5_1_17;
    property Item_5_1_18: integer read FItem_5_1_18 write FItem_5_1_18;
    property Item_5_1_19: integer read FItem_5_1_19 write FItem_5_1_19;
    property Item_5_1_20: integer read FItem_5_1_20 write FItem_5_1_20;
    property Item_5_1_21: string read FItem_5_1_21 write FItem_5_1_21;
    property Item_5_1_22: string read FItem_5_1_22 write FItem_5_1_22;

    property Item_5_2_1: string read FItem_5_2_1 write FItem_5_2_1;
    property Item_5_2_2: string read FItem_5_2_2 write FItem_5_2_2;
    property Item_5_2_3: string read FItem_5_2_3 write FItem_5_2_3;
    property Item_5_2_4: string read FItem_5_2_4 write FItem_5_2_4;
    property Item_5_2_5: integer read FItem_5_2_5 write FItem_5_2_5;
    property Item_5_2_6: integer read FItem_5_2_6 write FItem_5_2_6;
    property Item_5_2_7: double read FItem_5_2_7 write FItem_5_2_7;
    property Item_5_2_8: double read FItem_5_2_8 write FItem_5_2_8;
    property Item_5_2_9: double read FItem_5_2_9 write FItem_5_2_9;
    property Item_5_2_10: double read FItem_5_2_10 write FItem_5_2_10;
    property Item_5_2_11: double read FItem_5_2_11 write FItem_5_2_11;
    property Item_5_2_12: double read FItem_5_2_12 write FItem_5_2_12;
    property Item_5_2_13: double read FItem_5_2_13 write FItem_5_2_13;
    property Item_5_2_14: double read FItem_5_2_14 write FItem_5_2_14;
    property Item_5_2_15: double read FItem_5_2_15 write FItem_5_2_15;
    property Item_5_2_16: double read FItem_5_2_16 write FItem_5_2_16;
    property Item_5_2_17: double read FItem_5_2_17 write FItem_5_2_17;

    property Item_5_2_18: integer read FItem_5_2_18 write FItem_5_2_18;
    property Item_5_2_19: integer read FItem_5_2_19 write FItem_5_2_19;
    property Item_5_2_20: integer read FItem_5_2_20 write FItem_5_2_20;
    property Item_5_2_21: integer read FItem_5_2_21 write FItem_5_2_21;
    property Item_5_2_22: string read FItem_5_2_22 write FItem_5_2_22;
    property Item_5_2_23: string read FItem_5_2_23 write FItem_5_2_23;

    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;
  end;

implementation

end.
