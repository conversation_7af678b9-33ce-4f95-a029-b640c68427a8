unit LoadingTabThread;

interface

uses
  Classes, SysUtils, StdCtrls, Vcl.Forms, Cc_LoadingTabFrm;

type
  TThreadTabModel = class(TThread)
  private
    FLoadingTabForm: TLoadingTabForm;
    { Private declarations }
  protected
    procedure Execute; override;
    procedure DoTerminate; override;
    procedure CallVclMethod;
  public
    property LoadingTabForm: TLoadingTabForm read FLoadingTabForm
      write FLoadingTabForm;

  end;

var
  LoadingTabForm: TLoadingTabForm;

implementation

{ Important: Methods and properties of objects in visual components can only be
  used in a method called using Synchronize, for example,

  Synchronize(UpdateCaption);

  and UpdateCaption could look like,

  procedure threadModel.UpdateCaption;
  begin
  Form1.Caption := 'Updated in a thread';
  end; }

{ threadModel }

procedure TThreadTabModel.Execute;
begin
  // application.ProcessMessages;
  // while not Terminated do
  // begin
  // // Sleep(500);
  // Synchronize(CallVclMethod);
  // application.ProcessMessages;
  // end;
  { Place thread code here }
end;

procedure TThreadTabModel.CallVclMethod;
begin
  // LoadingForm.Show;
  // LoadingForm.Update;
  // LoadingForm.RxGIFAnimator1.Animate := true;
  // LoadingForm.Update;
end;

procedure TThreadTabModel.DoTerminate;
begin
  LoadingTabForm.Close;
end;

end.
