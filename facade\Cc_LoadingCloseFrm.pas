unit Cc_LoadingCloseFrm;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants,
  System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, RxAnimate, RxGIFCtrl, RxGIF,
  Vcl.ExtCtrls, Vcl.Imaging.jpeg, Vcl.StdCtrls, RzLabel, Vcl.Imaging.pngimage;

type
  TLoadingCloseForm = class(TForm)
    Image1: TImage;
    RzLabel1: TRzLabel;
    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
  end;

var
  LoadingCloseForm: TLoadingCloseForm;

implementation

{$R *.dfm}

procedure TLoadingCloseForm.FormCreate(Sender: TObject);
begin
  LoadingCloseForm.left := (screen.width - LoadingCloseForm.width) div 2;
  LoadingCloseForm.top := (screen.height - LoadingCloseForm.height) div 2;
end;

procedure TLoadingCloseForm.FormShow(Sender: TObject);
begin
  // self.RxGIFAnimator1.Animate := true;
end;

end.
