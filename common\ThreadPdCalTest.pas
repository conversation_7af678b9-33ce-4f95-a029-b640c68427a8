unit ThreadPdCalTest;

interface

uses
  Windows, SysUtils, Classes, Forms, Winapi.Messages, Vcl.Dialogs, System.Math,
  RzCommon, CcMaterial, CommonUtil, DMUtil, System.Variants, XxDd, ThreadPdCal;

type
  { 性能测试管理器 - 用于测试和比较原始方法与优化方法的性能 }
  TThreadPdCalTestManager = class
  private
    FThreadPdCal: TThreadPdCal;
    FTestResults: TStringList;
  public
    constructor Create;
    destructor Destroy; override;
    
    { 运行基本性能测试 }
    procedure RunBasicPerformanceTest;
    
    { 运行排序算法性能测试 }
    procedure RunSortingPerformanceTest;
    
    { 运行字符串转换性能测试 }
    procedure RunStringConversionTest;
    
    { 运行内存使用测试 }
    procedure RunMemoryUsageTest;
    
    { 获取测试结果 }
    function GetTestResults: string;
    
    { 保存测试结果到文件 }
    procedure SaveTestResults(const FileName: string);
  end;

implementation

{ TThreadPdCalTestManager }

constructor TThreadPdCalTestManager.Create;
begin
  inherited Create;
  FThreadPdCal := TThreadPdCal.Create;
  FTestResults := TStringList.Create;
end;

destructor TThreadPdCalTestManager.Destroy;
begin
  FThreadPdCal.Free;
  FTestResults.Free;
  inherited Destroy;
end;

{ 运行基本性能测试 }
procedure TThreadPdCalTestManager.RunBasicPerformanceTest;
var
  TestData: TStringList;
  i: Integer;
begin
  FTestResults.Add('=== 基本性能测试开始 ===');
  FTestResults.Add('测试时间: ' + DateTimeToStr(Now));
  
  TestData := TStringList.Create;
  try
    // 创建测试数据 - 模拟真实的生产排档数据
    TestData.Add('500');  // 尺码1数量
    TestData.Add('450');  // 尺码2数量
    TestData.Add('400');  // 尺码3数量
    TestData.Add('350');  // 尺码4数量
    TestData.Add('300');  // 尺码5数量
    TestData.Add('250');  // 尺码6数量
    TestData.Add('200');  // 尺码7数量
    TestData.Add('150');  // 尺码8数量
    TestData.Add('100');  // 尺码9数量
    TestData.Add('80');   // 尺码10数量
    TestData.Add('60');   // 尺码11数量
    TestData.Add('40');   // 尺码12数量
    TestData.Add('20');   // 尺码13数量
    
    FTestResults.Add('测试数据总量: ' + IntToStr(GetTestDataTotal(TestData)) + ' 件');
    
    // 运行性能测试
    FThreadPdCal.PerformanceTest(TestData);
    
  finally
    TestData.Free;
  end;
  
  FTestResults.Add('=== 基本性能测试结束 ===');
  FTestResults.Add('');
end;

{ 运行排序算法性能测试 }
procedure TThreadPdCalTestManager.RunSortingPerformanceTest;
var
  TestArray: array of Integer;
  TestArrayCopy: array of Integer;
  StartTime, EndTime: TDateTime;
  OriginalTime, OptimizedTime: Double;
  i: Integer;
begin
  FTestResults.Add('=== 排序算法性能测试开始 ===');
  
  // 创建测试数组
  SetLength(TestArray, 1000);
  SetLength(TestArrayCopy, 1000);
  
  // 填充随机数据
  Randomize;
  for i := 0 to 999 do
  begin
    TestArray[i] := Random(10000);
    TestArrayCopy[i] := TestArray[i];
  end;
  
  // 测试原始冒泡排序
  StartTime := Now;
  FThreadPdCal.BubbleSort(TestArray);
  EndTime := Now;
  OriginalTime := (EndTime - StartTime) * 24 * 60 * 60 * 1000;
  
  // 测试优化快速排序
  StartTime := Now;
  FThreadPdCal.BubbleSortOptimized(TestArrayCopy);
  EndTime := Now;
  OptimizedTime := (EndTime - StartTime) * 24 * 60 * 60 * 1000;
  
  FTestResults.Add('冒泡排序时间: ' + FormatFloat('0.00', OriginalTime) + ' 毫秒');
  FTestResults.Add('快速排序时间: ' + FormatFloat('0.00', OptimizedTime) + ' 毫秒');
  
  if OptimizedTime > 0 then
  begin
    FTestResults.Add('排序性能提升: ' + FormatFloat('0.00', (OriginalTime - OptimizedTime) / OriginalTime * 100) + '%');
  end;
  
  // 验证排序结果正确性
  if VerifySortResult(TestArray, TestArrayCopy) then
    FTestResults.Add('排序结果验证: 通过')
  else
    FTestResults.Add('排序结果验证: 失败');
  
  FTestResults.Add('=== 排序算法性能测试结束 ===');
  FTestResults.Add('');
  
  SetLength(TestArray, 0);
  SetLength(TestArrayCopy, 0);
end;

{ 运行字符串转换性能测试 }
procedure TThreadPdCalTestManager.RunStringConversionTest;
var
  TestStrings: TStringList;
  StartTime, EndTime: TDateTime;
  WithoutCacheTime, WithCacheTime: Double;
  i, j, Value: Integer;
begin
  FTestResults.Add('=== 字符串转换性能测试开始 ===');
  
  TestStrings := TStringList.Create;
  try
    // 创建测试字符串
    for i := 1 to 1000 do
    begin
      TestStrings.Add(IntToStr(i));
    end;
    
    // 测试不使用缓存的转换
    StartTime := Now;
    for j := 1 to 100 do
    begin
      for i := 0 to TestStrings.Count - 1 do
      begin
        Value := StrToInt(TestStrings.Strings[i]);
      end;
    end;
    EndTime := Now;
    WithoutCacheTime := (EndTime - StartTime) * 24 * 60 * 60 * 1000;
    
    // 测试使用缓存的转换
    StartTime := Now;
    for j := 1 to 100 do
    begin
      for i := 0 to TestStrings.Count - 1 do
      begin
        Value := FThreadPdCal.FStringCache.GetIntValue(TestStrings.Strings[i]);
      end;
    end;
    EndTime := Now;
    WithCacheTime := (EndTime - StartTime) * 24 * 60 * 60 * 1000;
    
    FTestResults.Add('不使用缓存时间: ' + FormatFloat('0.00', WithoutCacheTime) + ' 毫秒');
    FTestResults.Add('使用缓存时间: ' + FormatFloat('0.00', WithCacheTime) + ' 毫秒');
    
    if WithCacheTime > 0 then
    begin
      FTestResults.Add('缓存性能提升: ' + FormatFloat('0.00', (WithoutCacheTime - WithCacheTime) / WithoutCacheTime * 100) + '%');
    end;
    
  finally
    TestStrings.Free;
  end;
  
  FTestResults.Add('=== 字符串转换性能测试结束 ===');
  FTestResults.Add('');
end;

{ 运行内存使用测试 }
procedure TThreadPdCalTestManager.RunMemoryUsageTest;
var
  StringLists: array of TStringList;
  StartMemory, EndMemory: Cardinal;
  i: Integer;
begin
  FTestResults.Add('=== 内存使用测试开始 ===');
  
  // 获取初始内存使用量
  StartMemory := GetProcessMemUse(GetCurrentProcessId);
  
  // 测试对象池的内存效率
  SetLength(StringLists, 1000);
  
  // 从对象池获取对象
  for i := 0 to 999 do
  begin
    StringLists[i] := FThreadPdCal.FStringListPool.GetStringList;
    StringLists[i].Add('Test Data ' + IntToStr(i));
  end;
  
  // 返回对象到池中
  for i := 0 to 999 do
  begin
    FThreadPdCal.FStringListPool.ReturnStringList(StringLists[i]);
  end;
  
  // 获取结束内存使用量
  EndMemory := GetProcessMemUse(GetCurrentProcessId);
  
  FTestResults.Add('初始内存使用: ' + IntToStr(StartMemory) + ' KB');
  FTestResults.Add('结束内存使用: ' + IntToStr(EndMemory) + ' KB');
  FTestResults.Add('内存增长: ' + IntToStr(EndMemory - StartMemory) + ' KB');
  
  FTestResults.Add('=== 内存使用测试结束 ===');
  FTestResults.Add('');
  
  SetLength(StringLists, 0);
end;

{ 获取测试结果 }
function TThreadPdCalTestManager.GetTestResults: string;
begin
  Result := FTestResults.Text;
end;

{ 保存测试结果到文件 }
procedure TThreadPdCalTestManager.SaveTestResults(const FileName: string);
begin
  try
    FTestResults.SaveToFile(FileName);
  except
    on E: Exception do
    begin
      // 处理文件保存错误
      FTestResults.Add('保存测试结果失败: ' + E.Message);
    end;
  end;
end;

{ 辅助函数 - 计算测试数据总量 }
function GetTestDataTotal(TestData: TStringList): Integer;
var
  i: Integer;
begin
  Result := 0;
  for i := 0 to TestData.Count - 1 do
  begin
    try
      Result := Result + StrToInt(TestData.Strings[i]);
    except
      // 忽略转换错误
    end;
  end;
end;

{ 辅助函数 - 验证排序结果 }
function VerifySortResult(const Array1, Array2: array of Integer): Boolean;
var
  i: Integer;
begin
  Result := True;
  if Length(Array1) <> Length(Array2) then
  begin
    Result := False;
    Exit;
  end;
  
  for i := 0 to Length(Array1) - 1 do
  begin
    if Array1[i] <> Array2[i] then
    begin
      Result := False;
      Exit;
    end;
  end;
end;

{ 辅助函数 - 获取进程内存使用量 }
function GetProcessMemUse(ProcessID: Cardinal): Cardinal;
var
  hProcess: THandle;
  MemCounters: TProcessMemoryCounters;
begin
  Result := 0;
  hProcess := OpenProcess(PROCESS_QUERY_INFORMATION or PROCESS_VM_READ, False, ProcessID);
  try
    if hProcess <> 0 then
    begin
      if GetProcessMemoryInfo(hProcess, @MemCounters, SizeOf(MemCounters)) then
        Result := MemCounters.WorkingSetSize div 1024;
    end;
  finally
    CloseHandle(hProcess);
  end;
end;

end.
