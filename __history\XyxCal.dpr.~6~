program XyxCal;

uses
  Vcl.Forms,
  Cc_LoadingCloseFrm in 'facade\Cc_LoadingCloseFrm.pas' {LoadingCloseForm},
  Cc_LoadingFrm in 'facade\Cc_LoadingFrm.pas' {LoadingForm},
  Cc_LoadingPicFrm in 'facade\Cc_LoadingPicFrm.pas' {LoadingPicForm},
  Cc_LoadingSaveFpdFrm in 'facade\Cc_LoadingSaveFpdFrm.pas' {LoadingSaveFpdForm},
  Cc_LoadingSaveFrm in 'facade\Cc_LoadingSaveFrm.pas' {LoadingSaveForm},
  Cc_LoadingSmallFrm in 'facade\Cc_LoadingSmallFrm.pas' {LoadingSmallForm},
  Cc_LoadingTabFrm in 'facade\Cc_LoadingTabFrm.pas' {LoadingTabForm},
  Cc_OrdersPljsFrm in 'facade\Cc_OrdersPljsFrm.pas' {Cc_OrdersPljsForm},
  CommonUtil in 'common\CommonUtil.pas',
  DMUtil in 'common\DMUtil.pas' {DataModule1: TDataModule},
  LoadingCloseThread in 'common\LoadingCloseThread.pas',
  LoadingPicThread in 'common\LoadingPicThread.pas',
  LoadingSaveFpdThread in 'common\LoadingSaveFpdThread.pas',
  LoadingSaveThread in 'common\LoadingSaveThread.pas',
  LoadingSmallThread in 'common\LoadingSmallThread.pas',
  LoadingTabThread in 'common\LoadingTabThread.pas',
  LoadingThread in 'common\LoadingThread.pas',
  XxDd in 'common\XxDd.pas',
  CcMaterial in 'common\CcMaterial.pas',
  CcMaterialDetail in 'common\CcMaterialDetail.pas',
  CcMaterialFz in 'common\CcMaterialFz.pas',
  KsMaterialRecord in 'common\KsMaterialRecord.pas',
  CC_YlCalFrm in 'facade\CC_YlCalFrm.pas' {CalForm};

{$R *.res}

begin
  Application.Initialize;
  Application.MainFormOnTaskbar := True;
  Application.CreateForm(TCalForm, CalForm);
  Application.Run;
end.
