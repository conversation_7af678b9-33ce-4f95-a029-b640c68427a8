unit CcSjScjd;

interface

uses
  Classes;

type
  TCcSjScjd = class
  private
    FSjscjdid: Integer;
    FDdid: Integer;
    FScid: Integer;

    FDdh: string;
    FKh: string;
    FXdrq: string;
    FChrq: string;
    FYwpm: string;
    FZwpm: string;
    FKsbm: string;
    FSjh: string;
    FDds: Integer;
    FLbjDds: Integer;
    FNotOrderDds: Integer;
    FGs: Integer;
    FAddGs: Integer;
    FAddDds: Integer;
    FLb: string;
    FMl: string;
    FKs: string;
    FKz: string;
    FYs: string;
    FJg: double;

    FScJhBeginTime: string;

    FInfoStep_1: String;
    FInfoState_1: Integer;

    FInfoStep_2: String;
    FInfoState_2: Integer;

    FInfoStep_3: String;
    FInfoState_3: Integer;

    FInfoStep_4: String;
    FInfoState_4: Integer;

    FInfoStep_5: String;
    FInfoState_5: Integer;

    FInfoStep_6: String;
    FInfoState_6: Integer;

    FInfoStep_7: String;
    FInfoState_7: Integer;

    FInfoStep_8: String;
    FInfoState_8: Integer;

    FInfoStep_9: String;
    FInfoState_9: Integer;

    FInfoStep_10: String;
    FInfoState_10: Integer;

    FInfoStep_11: String;
    FInfoState_11: Integer;

    FInfoStep_12: String;
    FInfoState_12: Integer;

    FInfoStep_13: String;
    FInfoState_13: Integer;

    FInfoStep_14: String;
    FInfoState_14: Integer;

    FInfoStep_15: String;
    FInfoState_15: Integer;

    FInfoStep_16: String;
    FInfoState_16: Integer;

    FInfoStep_17: String;
    FInfoState_17: Integer;

    FInfoStep_18: String;
    FInfoState_18: Integer;

    FInfoStep_19: String;
    FInfoState_19: Integer;

    FInfoStep_20: String;
    FInfoState_20: Integer;

    FInfoStep_21: String;
    FInfoState_21: Integer;

    FInfoStep_22: String;
    FInfoState_22: Integer;

    FInfoStep_23: String;
    FInfoState_23: Integer;

    FInfoStep_24: String;
    FInfoState_24: Integer;

    FInfoStep_25: String;
    FInfoState_25: Integer;

    FInfoStep_26: String;
    FInfoState_26: Integer;

    FInfoStep_27: String;
    FInfoState_27: Integer;

    FInfoStep_28: String;
    FInfoState_28: Integer;

    FInfoStep_29: String;
    FInfoState_29: Integer;

    FInfoStep_30: String;
    FInfoState_30: Integer;

    FInfoStep_31: String;
    FInfoState_31: Integer;

    FInfoStep_32: String;
    FInfoState_32: Integer;

    FInfoStep_33: String;
    FInfoState_33: Integer;

    FInfoStep_34: String;
    FInfoState_34: Integer;

    FInfoStep_35: String;
    FInfoState_35: Integer;

    FMarketTime: string;
    FMarketUser: string;
    FState: string;

    FScxh: Integer;
    FScgs: double;
    FScState: string;
    FScCHRQ: string;
    FSccj: string;
    FScJqrq1: string;
    FScJqrq2: string;
    FScWcrq: string;
    Fcaij: string;
    FYinh: string;
    FXiuh: string;
    FHengj: string;
    FQit: string;
    FQit1: string;
    FShangx1: string;
    FXiax1: string;
    FQit2: string;
    FHouz: string;
    Fcaij_jd: double;
    FYinh_jd: double;
    FXiuh_jd: double;
    FHengj_jd: double;
    FQit_jd: double;
    FQit1_jd: double;
    FShangx1_jd: double;
    FXiax1_jd: double;
    FQit2_jd: double;
    FHouz_jd: double;
    FWanc: string;

    FMlDate: string;
  public
    property Sjscjdid: Integer read FSjscjdid write FSjscjdid;
    property Ddid: Integer read FDdid write FDdid;
    property Scid: Integer read FScid write FScid;

    property Ddh: string read FDdh write FDdh;
    property Kh: string read FKh write FKh;
    property Xdrq: string read FXdrq write FXdrq;
    property Chrq: string read FChrq write FChrq;
    property Ywpm: string read FYwpm write FYwpm;
    property Zwpm: string read FZwpm write FZwpm;
    property Ksbm: string read FKsbm write FKsbm;
    property Sjh: string read FSjh write FSjh;
    property Dds: Integer read FDds write FDds;
    property LbjDds: Integer read FLbjDds write FLbjDds;
    property NotOrderDds: Integer read FNotOrderDds write FNotOrderDds;
    property Gs: Integer read FGs write FGs;
    property AddGs: Integer read FAddGs write FAddGs;
    property AddDds: Integer read FAddDds write FAddDds;
    property Lb: string read FLb write FLb;
    property Ml: string read FMl write FMl;
    property Ks: string read FKs write FKs;
    property Kz: string read FKz write FKz;
    property Ys: string read FYs write FYs;
    property Jg: double read FJg write FJg;
    property ScJhBeginTime: string read FScJhBeginTime write FScJhBeginTime;

    property InfoStep_1: string read FInfoStep_1 write FInfoStep_1;
    property InfoState_1: Integer read FInfoState_1 write FInfoState_1;

    property InfoStep_2: string read FInfoStep_2 write FInfoStep_2;
    property InfoState_2: Integer read FInfoState_2 write FInfoState_2;

    property InfoStep_3: string read FInfoStep_3 write FInfoStep_3;
    property InfoState_3: Integer read FInfoState_3 write FInfoState_3;

    property InfoStep_4: string read FInfoStep_4 write FInfoStep_4;
    property InfoState_4: Integer read FInfoState_4 write FInfoState_4;

    property InfoStep_5: string read FInfoStep_5 write FInfoStep_5;
    property InfoState_5: Integer read FInfoState_5 write FInfoState_5;

    property InfoStep_6: string read FInfoStep_6 write FInfoStep_6;
    property InfoState_6: Integer read FInfoState_6 write FInfoState_6;

    property InfoStep_7: string read FInfoStep_7 write FInfoStep_7;
    property InfoState_7: Integer read FInfoState_7 write FInfoState_7;

    property InfoStep_8: string read FInfoStep_8 write FInfoStep_8;
    property InfoState_8: Integer read FInfoState_8 write FInfoState_8;

    property InfoStep_9: string read FInfoStep_9 write FInfoStep_9;
    property InfoState_9: Integer read FInfoState_9 write FInfoState_9;

    property InfoStep_10: string read FInfoStep_10 write FInfoStep_10;
    property InfoState_10: Integer read FInfoState_10 write FInfoState_10;

    property InfoStep_11: string read FInfoStep_11 write FInfoStep_11;
    property InfoState_11: Integer read FInfoState_11 write FInfoState_11;

    property InfoStep_12: string read FInfoStep_12 write FInfoStep_12;
    property InfoState_12: Integer read FInfoState_12 write FInfoState_12;

    property InfoStep_13: string read FInfoStep_13 write FInfoStep_13;
    property InfoState_13: Integer read FInfoState_13 write FInfoState_13;

    property InfoStep_14: string read FInfoStep_14 write FInfoStep_14;
    property InfoState_14: Integer read FInfoState_14 write FInfoState_14;

    property InfoStep_15: string read FInfoStep_15 write FInfoStep_15;
    property InfoState_15: Integer read FInfoState_15 write FInfoState_15;

    property InfoStep_16: string read FInfoStep_16 write FInfoStep_16;
    property InfoState_16: Integer read FInfoState_16 write FInfoState_16;

    property InfoStep_17: string read FInfoStep_17 write FInfoStep_17;
    property InfoState_17: Integer read FInfoState_17 write FInfoState_17;

    property InfoStep_18: string read FInfoStep_18 write FInfoStep_18;
    property InfoState_18: Integer read FInfoState_18 write FInfoState_18;

    property InfoStep_19: string read FInfoStep_19 write FInfoStep_19;
    property InfoState_19: Integer read FInfoState_19 write FInfoState_19;

    property InfoStep_20: string read FInfoStep_20 write FInfoStep_20;
    property InfoState_20: Integer read FInfoState_20 write FInfoState_20;

    property InfoStep_21: string read FInfoStep_21 write FInfoStep_21;
    property InfoState_21: Integer read FInfoState_21 write FInfoState_21;

    property InfoStep_22: string read FInfoStep_22 write FInfoStep_22;
    property InfoState_22: Integer read FInfoState_22 write FInfoState_22;

    property InfoStep_23: string read FInfoStep_23 write FInfoStep_23;
    property InfoState_23: Integer read FInfoState_23 write FInfoState_23;

    property InfoStep_24: string read FInfoStep_24 write FInfoStep_24;
    property InfoState_24: Integer read FInfoState_24 write FInfoState_24;

    property InfoStep_25: string read FInfoStep_25 write FInfoStep_25;
    property InfoState_25: Integer read FInfoState_25 write FInfoState_25;

    property InfoStep_26: string read FInfoStep_26 write FInfoStep_26;
    property InfoState_26: Integer read FInfoState_26 write FInfoState_26;

    property InfoStep_27: string read FInfoStep_27 write FInfoStep_27;
    property InfoState_27: Integer read FInfoState_27 write FInfoState_27;

    property InfoStep_28: string read FInfoStep_28 write FInfoStep_28;
    property InfoState_28: Integer read FInfoState_28 write FInfoState_28;

    property InfoStep_29: string read FInfoStep_29 write FInfoStep_29;
    property InfoState_29: Integer read FInfoState_29 write FInfoState_29;

    property InfoStep_30: string read FInfoStep_30 write FInfoStep_30;
    property InfoState_30: Integer read FInfoState_30 write FInfoState_30;

    property InfoStep_31: string read FInfoStep_31 write FInfoStep_31;
    property InfoState_31: Integer read FInfoState_31 write FInfoState_31;

    property InfoStep_32: string read FInfoStep_32 write FInfoStep_32;
    property InfoState_32: Integer read FInfoState_32 write FInfoState_32;

    property InfoStep_33: string read FInfoStep_33 write FInfoStep_33;
    property InfoState_33: Integer read FInfoState_33 write FInfoState_33;

    property InfoStep_34: string read FInfoStep_34 write FInfoStep_34;
    property InfoState_34: Integer read FInfoState_34 write FInfoState_34;

    property InfoStep_35: string read FInfoStep_35 write FInfoStep_35;
    property InfoState_35: Integer read FInfoState_35 write FInfoState_35;

    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;

    property Scgs: double read FScgs write FScgs;
    property Sccj: string read FSccj write FSccj;
    property Scxh: Integer read FScxh write FScxh;
    property ScState: string read FScState write FScState;
    property ScCHRQ: string read FScCHRQ write FScCHRQ;
    property ScJqrq1: string read FScJqrq1 write FScJqrq1;
    property ScJqrq2: string read FScJqrq2 write FScJqrq2;
    property ScWcrq: string read FScWcrq write FScWcrq;

    property caij: string read Fcaij write Fcaij;
    property Yinh: string read FYinh write FYinh;
    property Xiuh: string read FXiuh write FXiuh;
    property Hengj: string read FHengj write FHengj;
    property Qit: string read FQit write FQit;
    property Qit1: string read FQit1 write FQit1;
    property Shangx1: string read FShangx1 write FShangx1;
    property Xiax1: string read FXiax1 write FXiax1;
    property Qit2: string read FQit2 write FQit2;
    property Houz: string read FHouz write FHouz;

    property caij_jd: double read Fcaij_jd write Fcaij_jd;
    property Yinh_jd: double read FYinh_jd write FYinh_jd;
    property Xiuh_jd: double read FXiuh_jd write FXiuh_jd;
    property Hengj_jd: double read FHengj_jd write FHengj_jd;
    property Qit_jd: double read FQit_jd write FQit_jd;
    property Qit1_jd: double read FQit1_jd write FQit1_jd;
    property Shangx1_jd: double read FShangx1_jd write FShangx1_jd;
    property Xiax1_jd: double read FXiax1_jd write FXiax1_jd;
    property Qit2_jd: double read FQit2_jd write FQit2_jd;
    property Houz_jd: double read FHouz_jd write FHouz_jd;

    property Wanc: string read FWanc write FWanc;
    property MlDate: string read FMlDate write FMlDate;

  end;

implementation

end.
