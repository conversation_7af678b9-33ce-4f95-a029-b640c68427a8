object Cc_OrdersPljsForm: TCc_OrdersPljsForm
  Left = 0
  Top = 0
  ClientHeight = 408
  ClientWidth = 579
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = #23435#20307
  Font.Style = []
  OldCreateOrder = True
  Position = poScreenCenter
  PixelsPerInch = 96
  TextHeight = 12
  object RzPanel1: TRzPanel
    Left = 0
    Top = 0
    Width = 579
    Height = 200
    Align = alTop
    BorderOuter = fsNone
    Color = clWhite
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -12
    Font.Name = #24494#36719#38597#40657
    Font.Style = []
    ParentFont = False
    TabOrder = 0
    Visible = False
    object RzLabel1: TRzLabel
      Left = 979
      Top = 71
      Width = 75
      Height = 20
      Caption = #35774#32622#20214#25968#65306
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clGray
      Font.Height = -15
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      ParentFont = False
      Transparent = True
    end
    object RzLabel2: TRzLabel
      Left = 979
      Top = 156
      Width = 73
      Height = 20
      Caption = #20998'       '#20540#65306
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clGray
      Font.Height = -15
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      ParentFont = False
      Transparent = True
    end
    object RzLabel3: TRzLabel
      Left = 1122
      Top = 161
      Width = 18
      Height = 15
      Caption = '--'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -15
      Font.Name = #23435#20307
      Font.Style = [fsBold]
      ParentFont = False
      Transparent = True
    end
    object RzLabel4: TRzLabel
      Left = 164
      Top = 30
      Width = 45
      Height = 20
      Caption = #31867#21035#65306
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clGray
      Font.Height = -15
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      ParentFont = False
      Transparent = True
    end
    object RzLabel5: TRzLabel
      Left = 979
      Top = 113
      Width = 75
      Height = 20
      Caption = #26368#22823#23618#25968#65306
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clGray
      Font.Height = -15
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      ParentFont = False
      Transparent = True
    end
    object AdvStringGrid_t: TAdvStringGrid
      Left = 165
      Top = 69
      Width = 780
      Height = 115
      Cursor = crDefault
      Align = alCustom
      BevelInner = bvNone
      BevelOuter = bvNone
      ColCount = 12
      Ctl3D = False
      DefaultRowHeight = 26
      DoubleBuffered = False
      DrawingStyle = gdsClassic
      FixedColor = clWhite
      FixedCols = 0
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clBlack
      Font.Height = -12
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goEditing]
      ParentCtl3D = False
      ParentDoubleBuffered = False
      ParentFont = False
      PopupMenu = PopupMenu2
      ScrollBars = ssBoth
      TabOrder = 0
      OnKeyDown = AdvStringGrid_tKeyDown
      GridLineColor = 15855083
      GridFixedLineColor = 13745060
      HoverRowCells = [hcNormal, hcSelected]
      OnCellValidate = AdvStringGrid_tCellValidate
      HighlightColor = clNone
      ActiveCellFont.Charset = DEFAULT_CHARSET
      ActiveCellFont.Color = clWindowText
      ActiveCellFont.Height = -12
      ActiveCellFont.Name = #23435#20307
      ActiveCellFont.Style = [fsBold]
      ActiveCellColor = 10344697
      ActiveCellColorTo = 6210033
      ColumnHeaders.Strings = (
        #32534#21495
        #21517#31216)
      ControlLook.FixedGradientFrom = 16513526
      ControlLook.FixedGradientTo = 15260626
      ControlLook.FixedGradientHoverFrom = 15000287
      ControlLook.FixedGradientHoverTo = 14406605
      ControlLook.FixedGradientHoverMirrorFrom = 14406605
      ControlLook.FixedGradientHoverMirrorTo = 13813180
      ControlLook.FixedGradientHoverBorder = 12033927
      ControlLook.FixedGradientDownFrom = 14991773
      ControlLook.FixedGradientDownTo = 14991773
      ControlLook.FixedGradientDownMirrorFrom = 14991773
      ControlLook.FixedGradientDownMirrorTo = 14991773
      ControlLook.FixedGradientDownBorder = 14991773
      ControlLook.DropDownHeader.Font.Charset = DEFAULT_CHARSET
      ControlLook.DropDownHeader.Font.Color = clWindowText
      ControlLook.DropDownHeader.Font.Height = -11
      ControlLook.DropDownHeader.Font.Name = 'Tahoma'
      ControlLook.DropDownHeader.Font.Style = []
      ControlLook.DropDownHeader.Visible = True
      ControlLook.DropDownHeader.Buttons = <>
      ControlLook.DropDownFooter.Font.Charset = DEFAULT_CHARSET
      ControlLook.DropDownFooter.Font.Color = clWindowText
      ControlLook.DropDownFooter.Font.Height = -11
      ControlLook.DropDownFooter.Font.Name = 'Tahoma'
      ControlLook.DropDownFooter.Font.Style = []
      ControlLook.DropDownFooter.Visible = True
      ControlLook.DropDownFooter.Buttons = <>
      Filter = <>
      FilterDropDown.Font.Charset = DEFAULT_CHARSET
      FilterDropDown.Font.Color = clWindowText
      FilterDropDown.Font.Height = -12
      FilterDropDown.Font.Name = #24494#36719#38597#40657
      FilterDropDown.Font.Style = []
      FilterDropDown.TextChecked = 'Checked'
      FilterDropDown.TextUnChecked = 'Unchecked'
      FilterDropDownClear = '(All)'
      FilterEdit.TypeNames.Strings = (
        'Starts with'
        'Ends with'
        'Contains'
        'Not contains'
        'Equal'
        'Not equal'
        'Clear')
      FixedColWidth = 80
      FixedRowHeight = 26
      FixedRowAlways = True
      FixedFont.Charset = DEFAULT_CHARSET
      FixedFont.Color = clBlack
      FixedFont.Height = -12
      FixedFont.Name = #24494#36719#38597#40657
      FixedFont.Style = [fsBold]
      Flat = True
      FloatFormat = '%.2f'
      HoverButtons.Buttons = <>
      HoverButtons.Position = hbLeftFromColumnLeft
      HTMLSettings.ImageFolder = 'images'
      HTMLSettings.ImageBaseName = 'img'
      Look = glOffice2007
      PrintSettings.DateFormat = 'dd/mm/yyyy'
      PrintSettings.Font.Charset = DEFAULT_CHARSET
      PrintSettings.Font.Color = clWindowText
      PrintSettings.Font.Height = -11
      PrintSettings.Font.Name = 'Tahoma'
      PrintSettings.Font.Style = []
      PrintSettings.FixedFont.Charset = DEFAULT_CHARSET
      PrintSettings.FixedFont.Color = clWindowText
      PrintSettings.FixedFont.Height = -11
      PrintSettings.FixedFont.Name = 'Tahoma'
      PrintSettings.FixedFont.Style = []
      PrintSettings.HeaderFont.Charset = DEFAULT_CHARSET
      PrintSettings.HeaderFont.Color = clWindowText
      PrintSettings.HeaderFont.Height = -11
      PrintSettings.HeaderFont.Name = 'Tahoma'
      PrintSettings.HeaderFont.Style = []
      PrintSettings.FooterFont.Charset = DEFAULT_CHARSET
      PrintSettings.FooterFont.Color = clWindowText
      PrintSettings.FooterFont.Height = -11
      PrintSettings.FooterFont.Name = 'Tahoma'
      PrintSettings.FooterFont.Style = []
      PrintSettings.PageNumSep = '/'
      SearchFooter.Color = 16513526
      SearchFooter.ColorTo = clNone
      SearchFooter.FindNextCaption = 'Find &next'
      SearchFooter.FindPrevCaption = 'Find &previous'
      SearchFooter.Font.Charset = DEFAULT_CHARSET
      SearchFooter.Font.Color = clWindowText
      SearchFooter.Font.Height = -11
      SearchFooter.Font.Name = 'Tahoma'
      SearchFooter.Font.Style = []
      SearchFooter.HighLightCaption = 'Highlight'
      SearchFooter.HintClose = 'Close'
      SearchFooter.HintFindNext = 'Find next occurrence'
      SearchFooter.HintFindPrev = 'Find previous occurrence'
      SearchFooter.HintHighlight = 'Highlight occurrences'
      SearchFooter.MatchCaseCaption = 'Match case'
      SelectionColor = clSkyBlue
      ShowDesignHelper = False
      SortSettings.DefaultFormat = ssAutomatic
      URLUnderlineOnHover = True
      UseInternalHintClass = False
      VAlignment = vtaCenter
      Version = '8.1.3.0'
      ColWidths = (
        80
        46
        43
        46
        25
        39
        36
        29
        23
        69
        64
        64)
    end
    object Edit_Js: TRzEdit
      Left = 1065
      Top = 69
      Width = 51
      Height = 28
      Text = '7'
      Alignment = taRightJustify
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -15
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      ParentFont = False
      TabOrder = 1
    end
    object Edit_fz_b: TRzEdit
      Left = 1065
      Top = 156
      Width = 51
      Height = 28
      Text = '3.8'
      Alignment = taRightJustify
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -15
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      ParentFont = False
      TabOrder = 2
    end
    object Edit_fz_e: TRzEdit
      Left = 1149
      Top = 156
      Width = 51
      Height = 28
      Text = '5.5'
      Alignment = taRightJustify
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -15
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      ParentFont = False
      TabOrder = 3
    end
    object Button3: TButton
      Left = 738
      Top = 190
      Width = 75
      Height = 34
      Caption = #35745#31639#26041#26696'new'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -12
      Font.Name = #23435#20307
      Font.Style = []
      ParentFont = False
      TabOrder = 4
    end
    object Edit_Cs: TRzEdit
      Left = 1065
      Top = 112
      Width = 51
      Height = 28
      Text = '200'
      Alignment = taRightJustify
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -15
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      ParentFont = False
      TabOrder = 5
    end
    object RzCheckBox1: TRzCheckBox
      Left = 404
      Top = 28
      Width = 71
      Height = 21
      Caption = #22810#26465#25490#20992
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -13
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      ParentFont = False
      State = cbUnchecked
      TabOrder = 6
    end
    object Btn_Query: TAdvGlowButton
      Left = 759
      Top = 32
      Width = 82
      Height = 28
      Caption = #28165'  '#31354
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clBlack
      Font.Height = -13
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      NotesFont.Charset = DEFAULT_CHARSET
      NotesFont.Color = clWindowText
      NotesFont.Height = -11
      NotesFont.Name = 'Tahoma'
      NotesFont.Style = []
      ParentFont = False
      Spacing = 10
      TabOrder = 7
      Appearance.BorderColor = 12631218
      Appearance.BorderColorHot = 10079963
      Appearance.BorderColorDown = 4548219
      Appearance.ColorTo = 16316405
      Appearance.ColorChecked = 7915518
      Appearance.ColorCheckedTo = 11918331
      Appearance.ColorDisabled = clBtnFace
      Appearance.ColorDisabledTo = clBtnFace
      Appearance.ColorDown = 12631218
      Appearance.ColorDownTo = 12631218
      Appearance.ColorHot = clWhite
      Appearance.ColorHotTo = 16316405
      Appearance.ColorMirror = 16316405
      Appearance.ColorMirrorTo = 16316405
      Appearance.ColorMirrorHot = 16316405
      Appearance.ColorMirrorHotTo = 16316405
      Appearance.ColorMirrorDown = 12631218
      Appearance.ColorMirrorDownTo = 12631218
      Appearance.ColorMirrorChecked = 10480637
      Appearance.ColorMirrorCheckedTo = 5682430
      Appearance.ColorMirrorDisabled = clBtnFace
      Appearance.ColorMirrorDisabledTo = clBtnFace
      Appearance.GradientHot = ggVertical
      Appearance.GradientMirrorHot = ggVertical
      Appearance.GradientDown = ggVertical
      Appearance.GradientMirrorDown = ggVertical
      Appearance.GradientChecked = ggVertical
      Appearance.SystemFont = False
    end
    object AdvGlowButton1: TAdvGlowButton
      Left = 165
      Top = 190
      Width = 82
      Height = 28
      Caption = #25490'  '#20992
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clBlack
      Font.Height = -13
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      NotesFont.Charset = DEFAULT_CHARSET
      NotesFont.Color = clWindowText
      NotesFont.Height = -11
      NotesFont.Name = 'Tahoma'
      NotesFont.Style = []
      ParentFont = False
      Spacing = 10
      TabOrder = 8
      OnClick = AdvGlowButton1Click
      Appearance.BorderColor = 12631218
      Appearance.BorderColorHot = 10079963
      Appearance.BorderColorDown = 4548219
      Appearance.ColorTo = 16316405
      Appearance.ColorChecked = 7915518
      Appearance.ColorCheckedTo = 11918331
      Appearance.ColorDisabled = clBtnFace
      Appearance.ColorDisabledTo = clBtnFace
      Appearance.ColorDown = 12631218
      Appearance.ColorDownTo = 12631218
      Appearance.ColorHot = clWhite
      Appearance.ColorHotTo = 16316405
      Appearance.ColorMirror = 16316405
      Appearance.ColorMirrorTo = 16316405
      Appearance.ColorMirrorHot = 16316405
      Appearance.ColorMirrorHotTo = 16316405
      Appearance.ColorMirrorDown = 12631218
      Appearance.ColorMirrorDownTo = 12631218
      Appearance.ColorMirrorChecked = 10480637
      Appearance.ColorMirrorCheckedTo = 5682430
      Appearance.ColorMirrorDisabled = clBtnFace
      Appearance.ColorMirrorDisabledTo = clBtnFace
      Appearance.GradientHot = ggVertical
      Appearance.GradientMirrorHot = ggVertical
      Appearance.GradientDown = ggVertical
      Appearance.GradientMirrorDown = ggVertical
      Appearance.GradientChecked = ggVertical
      Appearance.SystemFont = False
    end
    object Button1: TButton
      Left = 275
      Top = 190
      Width = 75
      Height = 25
      Caption = #25490#20992'1'
      TabOrder = 9
    end
    object RzComboBox1: TRzComboBox
      Left = 223
      Top = 28
      Width = 145
      Height = 25
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -12
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      ParentFont = False
      TabOrder = 10
      Text = #30007#24335
      OnChange = RzComboBox1Change
      Items.Strings = (
        #30007#24335
        #22899#24335
        #20799#31461)
    end
  end
  object RzPanel2: TRzPanel
    Left = 0
    Top = 200
    Width = 579
    Height = 208
    Align = alClient
    BorderOuter = fsNone
    Color = clWhite
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -12
    Font.Name = #23435#20307
    Font.Style = []
    ParentFont = False
    TabOrder = 1
    Visible = False
    object RzLabel7: TRzLabel
      Left = 164
      Top = 30
      Width = 75
      Height = 19
      Caption = #25490#20992#32467#26524#65306
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -15
      Font.Name = #24494#36719#38597#40657
      Font.Style = [fsBold]
      ParentFont = False
      Transparent = True
    end
    object RzLabel6: TRzLabel
      Left = 245
      Top = 30
      Width = 105
      Height = 19
      Caption = #27809#26377#25490#20992#32467#26524#12290
      Color = clRed
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clRed
      Font.Height = -15
      Font.Name = #24494#36719#38597#40657
      Font.Style = [fsBold]
      ParentColor = False
      ParentFont = False
      Transparent = True
      Visible = False
    end
    object AdvStringGrid2t: TAdvStringGrid
      Left = 164
      Top = 70
      Width = 780
      Height = 252
      Cursor = crDefault
      Align = alCustom
      BevelInner = bvNone
      BevelOuter = bvNone
      ColCount = 13
      Ctl3D = False
      DefaultRowHeight = 26
      DoubleBuffered = False
      DrawingStyle = gdsClassic
      FixedColor = clWhite
      FixedCols = 0
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clBlack
      Font.Height = -12
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRowSelect]
      ParentCtl3D = False
      ParentDoubleBuffered = False
      ParentFont = False
      PopupMenu = PopupMenu2
      ScrollBars = ssNone
      TabOrder = 0
      Visible = False
      GridLineColor = 15855083
      GridFixedLineColor = 13745060
      HoverRowCells = [hcNormal, hcSelected]
      HighlightColor = clNone
      ActiveCellFont.Charset = DEFAULT_CHARSET
      ActiveCellFont.Color = clWindowText
      ActiveCellFont.Height = -12
      ActiveCellFont.Name = #23435#20307
      ActiveCellFont.Style = [fsBold]
      ActiveCellColor = 10344697
      ActiveCellColorTo = 6210033
      ColumnHeaders.Strings = (
        #32534#21495
        #21517#31216)
      ControlLook.FixedGradientFrom = 16513526
      ControlLook.FixedGradientTo = 15260626
      ControlLook.FixedGradientHoverFrom = 15000287
      ControlLook.FixedGradientHoverTo = 14406605
      ControlLook.FixedGradientHoverMirrorFrom = 14406605
      ControlLook.FixedGradientHoverMirrorTo = 13813180
      ControlLook.FixedGradientHoverBorder = 12033927
      ControlLook.FixedGradientDownFrom = 14991773
      ControlLook.FixedGradientDownTo = 14991773
      ControlLook.FixedGradientDownMirrorFrom = 14991773
      ControlLook.FixedGradientDownMirrorTo = 14991773
      ControlLook.FixedGradientDownBorder = 14991773
      ControlLook.DropDownHeader.Font.Charset = DEFAULT_CHARSET
      ControlLook.DropDownHeader.Font.Color = clWindowText
      ControlLook.DropDownHeader.Font.Height = -11
      ControlLook.DropDownHeader.Font.Name = 'Tahoma'
      ControlLook.DropDownHeader.Font.Style = []
      ControlLook.DropDownHeader.Visible = True
      ControlLook.DropDownHeader.Buttons = <>
      ControlLook.DropDownFooter.Font.Charset = DEFAULT_CHARSET
      ControlLook.DropDownFooter.Font.Color = clWindowText
      ControlLook.DropDownFooter.Font.Height = -11
      ControlLook.DropDownFooter.Font.Name = 'Tahoma'
      ControlLook.DropDownFooter.Font.Style = []
      ControlLook.DropDownFooter.Visible = True
      ControlLook.DropDownFooter.Buttons = <>
      Filter = <>
      FilterDropDown.Font.Charset = DEFAULT_CHARSET
      FilterDropDown.Font.Color = clWindowText
      FilterDropDown.Font.Height = -12
      FilterDropDown.Font.Name = #24494#36719#38597#40657
      FilterDropDown.Font.Style = []
      FilterDropDown.TextChecked = 'Checked'
      FilterDropDown.TextUnChecked = 'Unchecked'
      FilterDropDownClear = '(All)'
      FilterEdit.TypeNames.Strings = (
        'Starts with'
        'Ends with'
        'Contains'
        'Not contains'
        'Equal'
        'Not equal'
        'Clear')
      FixedColWidth = 80
      FixedRowHeight = 26
      FixedRowAlways = True
      FixedFont.Charset = DEFAULT_CHARSET
      FixedFont.Color = clBlack
      FixedFont.Height = -12
      FixedFont.Name = #24494#36719#38597#40657
      FixedFont.Style = [fsBold]
      Flat = True
      FloatFormat = '%.2f'
      HoverButtons.Buttons = <>
      HoverButtons.Position = hbLeftFromColumnLeft
      HTMLSettings.ImageFolder = 'images'
      HTMLSettings.ImageBaseName = 'img'
      Look = glOffice2007
      PrintSettings.DateFormat = 'dd/mm/yyyy'
      PrintSettings.Font.Charset = DEFAULT_CHARSET
      PrintSettings.Font.Color = clWindowText
      PrintSettings.Font.Height = -11
      PrintSettings.Font.Name = 'Tahoma'
      PrintSettings.Font.Style = []
      PrintSettings.FixedFont.Charset = DEFAULT_CHARSET
      PrintSettings.FixedFont.Color = clWindowText
      PrintSettings.FixedFont.Height = -11
      PrintSettings.FixedFont.Name = 'Tahoma'
      PrintSettings.FixedFont.Style = []
      PrintSettings.HeaderFont.Charset = DEFAULT_CHARSET
      PrintSettings.HeaderFont.Color = clWindowText
      PrintSettings.HeaderFont.Height = -11
      PrintSettings.HeaderFont.Name = 'Tahoma'
      PrintSettings.HeaderFont.Style = []
      PrintSettings.FooterFont.Charset = DEFAULT_CHARSET
      PrintSettings.FooterFont.Color = clWindowText
      PrintSettings.FooterFont.Height = -11
      PrintSettings.FooterFont.Name = 'Tahoma'
      PrintSettings.FooterFont.Style = []
      PrintSettings.PageNumSep = '/'
      SearchFooter.Color = 16513526
      SearchFooter.ColorTo = clNone
      SearchFooter.FindNextCaption = 'Find &next'
      SearchFooter.FindPrevCaption = 'Find &previous'
      SearchFooter.Font.Charset = DEFAULT_CHARSET
      SearchFooter.Font.Color = clWindowText
      SearchFooter.Font.Height = -11
      SearchFooter.Font.Name = 'Tahoma'
      SearchFooter.Font.Style = []
      SearchFooter.HighLightCaption = 'Highlight'
      SearchFooter.HintClose = 'Close'
      SearchFooter.HintFindNext = 'Find next occurrence'
      SearchFooter.HintFindPrev = 'Find previous occurrence'
      SearchFooter.HintHighlight = 'Highlight occurrences'
      SearchFooter.MatchCaseCaption = 'Match case'
      SelectionColor = 6210033
      ShowSelection = False
      SortSettings.DefaultFormat = ssAutomatic
      URLUnderlineOnHover = True
      UseInternalHintClass = False
      VAlignment = vtaCenter
      Version = '8.1.3.0'
      ColWidths = (
        80
        270
        122
        64
        64
        64
        64
        64
        64
        64
        64
        64
        64)
    end
    object RzPanel46: TRzPanel
      Left = 0
      Top = 0
      Width = 579
      Height = 18
      Align = alTop
      BorderOuter = fsNone
      BorderSides = [sdLeft, sdTop, sdRight]
      BorderColor = 14671839
      BorderWidth = 1
      Color = 16049103
      DoubleBuffered = False
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      GradientColorStyle = gcsCustom
      GradientColorStart = 16643306
      GradientColorStop = 16049103
      ParentDoubleBuffered = False
      ParentFont = False
      TabOrder = 1
      VisualStyle = vsGradient
    end
  end
  object RzPanel3: TRzPanel
    Left = 0
    Top = 200
    Width = 579
    Height = 208
    Align = alClient
    BorderOuter = fsNone
    Color = clWhite
    TabOrder = 2
    object TopLeftPanel: TRzPanel
      Left = 0
      Top = 0
      Width = 579
      Height = 208
      Align = alClient
      BorderOuter = fsNone
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      ParentFont = False
      TabOrder = 0
      Transparent = True
      object RzLabel8: TRzLabel
        Left = 15
        Top = 9
        Width = 82
        Height = 26
        Caption = #25490#26723' '#35745#31639
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlue
        Font.Height = -19
        Font.Name = #24494#36719#38597#40657
        Font.Style = [fsBold]
        ParentFont = False
        Transparent = True
      end
      object RzPanel4: TRzPanel
        Left = 152
        Top = 89
        Width = 288
        Height = 92
        BorderOuter = fsFlat
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        ParentFont = False
        TabOrder = 0
        Transparent = True
        object RzLabel43: TRzLabel
          Left = 37
          Top = 24
          Width = 204
          Height = 20
          Caption = #25490#26723#35745#31639#26102#38388#36739#38271#65292#35831#31245#20505#8230#8230
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -15
          Font.Name = #24494#36719#38597#40657
          Font.Style = []
          ParentFont = False
          Transparent = True
        end
        object RxGIFAnimator2: TRxGIFAnimator
          Left = 59
          Top = 63
          Width = 166
          Height = 10
          AsyncDrawing = True
          Animate = True
          Center = True
          FrameIndex = 3
          Image.Data = {
            F2050000474946383961D6000F00B30000FFFFFFE0FFE0ACE7ACA4E4A475CE75
            66CC6699999941B7412BAD2B0A9D0A009900FE01020000000000000000000000
            0021FF0B4E45545343415045322E30030100000021F9040508000B002C000000
            00D6000F00000467D0C849ABBD38EBCDBBFF60288E64059C68AAAE6CEBBE702C
            CF746DDF787E4A7AEFFFC0A07058E3118FC8A472D933329FD0A814E89C5AAF58
            6C35CBED7A83DBAF784C7685CBE8B4F7AC6EBBA1ECB77C2E8CD3EF789B3DCFEF
            B3F67E817D258485868788898A23110021F9040508000B002C030002000C000B
            0000042430C849A5B8385FC2BBE747288E61629EA8A9AC6CEBBE708CCC743D17
            78AEE343EFFFBD080021F9040508000B002C110002000C000B0000042430C849
            A5B8385FC2BBE747288E61629EA8A9AC6CEBBE708CCC743D1778AEE343EFFFBD
            080021F9040508000B002C1F0002000C000B0000042430C849A5B8385FC2BBE7
            47288E61629EA8A9AC6CEBBE708CCC743D1778AEE343EFFFBD080021F9040508
            000B002C2D0002000C000B0000042430C849A5B8385FC2BBE747288E61629EA8
            A9AC6CEBBE708CCC743D1778AEE343EFFFBD080021F9040508000B002C3B0002
            000C000B0000042430C849A5B8385FC2BBE747288E61629EA8A9AC6CEBBE708C
            CC743D1778AEE343EFFFBD080021F9040508000B002C490002000C000B000004
            2430C849A5B8385FC2BBE747288E61629EA8A9AC6CEBBE708CCC743D1778AEE3
            43EFFFBD080021F9040508000B002C570002000C000B0000042430C849A5B838
            5FC2BBE747288E61629EA8A9AC6CEBBE708CCC743D1778AEE343EFFFBD080021
            F9040508000B002C650002000C000B0000042430C849A5B8385FC2BBE747288E
            61629EA8A9AC6CEBBE708CCC743D1778AEE343EFFFBD080021F9040508000B00
            2C730002000C000B0000042430C849A5B8385FC2BBE747288E61629EA8A9AC6C
            EBBE708CCC743D1778AEE343EFFFBD080021F9040508000B002C810002000C00
            0B0000042430C849A5B8385FC2BBE747288E61629EA8A9AC6CEBBE708CCC743D
            1778AEE343EFFFBD080021F9040508000B002C8F0002000C000B0000042430C8
            49A5B8385FC2BBE747288E61629EA8A9AC6CEBBE708CCC743D1778AEE343EFFF
            BD080021F9040508000B002C9D0002000C000B0000042430C849A5B8385FC2BB
            E747288E61629EA8A9AC6CEBBE708CCC743D1778AEE343EFFFBD080021F90405
            08000B002CAB0002000C000B0000042430C849A5B8385FC2BBE747288E61629E
            A8A9AC6CEBBE708CCC743D1778AEE343EFFFBD080021F9040508000B002CB900
            02000C000B0000042430C849A5B8385FC2BBE747288E61629EA8A9AC6CEBBE70
            8CCC743D1778AEE343EFFFBD080021F9040508000B002CC70002000C000B0000
            042430C849A5B8385FC2BBE747288E61629EA8A9AC6CEBBE708CCC743D1778AE
            E343EFFFBD080021F9040508000B002C030002000C000B0000040C10C849ABBD
            38EBCDBBFF5C040021F9040508000B002C110002000C000B0000040C10C849AB
            BD38EBCDBBFF5C040021F9040508000B002C1F0002000C000B0000040C10C849
            ABBD38EBCDBBFF5C040021F9040508000B002C2D0002000C000B0000040C10C8
            49ABBD38EBCDBBFF5C040021F9040508000B002C3B0002000C000B0000040C10
            C849ABBD38EBCDBBFF5C040021F9040508000B002C490002000C000B0000040C
            10C849ABBD38EBCDBBFF5C040021F9040508000B002C570002000C000B000004
            0C10C849ABBD38EBCDBBFF5C040021F9040508000B002C650002000C000B0000
            040C10C849ABBD38EBCDBBFF5C040021F9040508000B002C730002000C000B00
            00040C10C849ABBD38EBCDBBFF5C040021F9040508000B002C810002000C000B
            0000040C10C849ABBD38EBCDBBFF5C040021F9040508000B002C8F0002000C00
            0B0000040C10C849ABBD38EBCDBBFF5C040021F9040508000B002C9D0002000C
            000B0000040C10C849ABBD38EBCDBBFF5C040021F9040508000B002CAB000200
            0C000B0000040C10C849ABBD38EBCDBBFF5C040021F9040508000B002CB90002
            000C000B0000040C10C849ABBD38EBCDBBFF5C04003B}
        end
      end
      object Memo2: TMemo
        Left = 164
        Top = 6
        Width = 185
        Height = 89
        Lines.Strings = (
          'Memo2')
        TabOrder = 1
        Visible = False
      end
    end
  end
  object Memo1: TMemo
    Left = 37
    Top = 8
    Width = 100
    Height = 326
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -12
    Font.Name = #23435#20307
    Font.Style = []
    ParentFont = False
    TabOrder = 3
    Visible = False
  end
  object OpenDialog1: TOpenDialog
    Options = [ofHideReadOnly, ofAllowMultiSelect, ofEnableSizing]
    Left = 888
    Top = 16
  end
  object PopupMenu1: TPopupMenu
    Left = 976
    Top = 16
    object N1: TMenuItem
      Caption = #31896#24086#21040#36719#20214#20013
    end
  end
  object PopupMenu2: TPopupMenu
    Left = 936
    Top = 8
    object N2: TMenuItem
      Caption = #22797#21046#32467#26524
    end
  end
  object Timer1: TTimer
    Enabled = False
    OnTimer = Timer1Timer
    Left = 400
    Top = 216
  end
  object Timer2: TTimer
    Enabled = False
    Left = 448
    Top = 216
  end
end
