unit LoadingThread;

interface

uses
  Classes, SysUtils, StdCtrls, Vcl.Forms, Cc_LoadingFrm;

type
  TThreadModel = class(TThread)
  private
    FLoadingForm: TLoadingForm;
    { Private declarations }
  protected
    procedure Execute; override;
    procedure DoTerminate; override;
    procedure CallVclMethod;
  public
    property LoadingForm: TLoadingForm read FLoadingForm write FLoadingForm;

  end;

var
  LoadingForm: TLoadingForm;

implementation

{ Important: Methods and properties of objects in visual components can only be
  used in a method called using Synchronize, for example,

  Synchronize(UpdateCaption);

  and UpdateCaption could look like,

  procedure threadModel.UpdateCaption;
  begin
  Form1.Caption := 'Updated in a thread';
  end; }

{ threadModel }

procedure TThreadModel.Execute;
begin
  // application.ProcessMessages;
  // while not Terminated do
  // begin
  // // Sleep(500);
  //Synchronize(CallVclMethod);
  // application.ProcessMessages;
  // end;
  { Place thread code here }
end;

procedure TThreadModel.CallVclMethod;
begin
  // LoadingForm.Show;
  // LoadingForm.Update;
//  LoadingForm.RxGIFAnimator1.Animate := true;
//  LoadingForm.Update;
end;

procedure TThreadModel.DoTerminate;
begin
  LoadingForm.Close;
end;

end.
