unit LoadingSmallThread;

interface

uses
  Classes, SysUtils, StdCtrls, Vcl.Forms, Cc_LoadingSmallFrm;

type
  TThreadSmallModel = class(TThread)
  private
    FLoadingSmallForm: TLoadingSmallForm;
    { Private declarations }
  protected
    procedure Execute; override;
    procedure DoTerminate; override;
    procedure CallVclMethod;
  public
    property LoadingSmallForm: TLoadingSmallForm read FLoadingSmallForm
      write FLoadingSmallForm;

  end;

var
  LoadingSmallForm: TLoadingSmallForm;

implementation

{ Important: Methods and properties of objects in visual components can only be
  used in a method called using Synchronize, for example,

  Synchronize(UpdateCaption);

  and UpdateCaption could look like,

  procedure threadModel.UpdateCaption;
  begin
  Form1.Caption := 'Updated in a thread';
  end; }

{ threadModel }

procedure TThreadSmallModel.Execute;
begin
  // application.ProcessMessages;
  // while not Terminated do
  // begin
  // // Sleep(500);
  // Synchronize(CallVclMethod);
  // application.ProcessMessages;
  // end;
  { Place thread code here }
end;

procedure TThreadSmallModel.CallVclMethod;
begin
  // LoadingForm.Show;
  // LoadingForm.Update;
  // LoadingForm.RxGIFAnimator1.Animate := true;
  // LoadingForm.Update;
end;

procedure TThreadSmallModel.DoTerminate;
begin
  LoadingSmallForm.Close;
end;

end.
