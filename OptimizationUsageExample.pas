unit OptimizationUsageExample;

{
  生产排档计算优化使用示例
  
  本单元展示了如何使用优化后的ThreadPdCal类来提升生产排档计算性能。
  主要优化包括：
  1. 快速排序替代冒泡排序
  2. 字符串转换缓存
  3. 对象池管理
  4. 优化的核心计算方法
}

interface

uses
  Windows, SysUtils, Classes, Forms, Vcl.Dialogs,
  ThreadPdCal, ThreadPdCalTest, CommonUtil, DMUtil;

type
  { 优化使用示例管理器 }
  TOptimizationExample = class
  private
    FThreadPdCal: TThreadPdCal;
    FTestManager: TThreadPdCalTestManager;
  public
    constructor Create;
    destructor Destroy; override;
    
    { 示例1: 基本优化使用 }
    procedure Example1_BasicOptimization;
    
    { 示例2: 性能对比测试 }
    procedure Example2_PerformanceComparison;
    
    { 示例3: 在现有代码中集成优化 }
    procedure Example3_IntegrateInExistingCode;
    
    { 示例4: 批量处理优化 }
    procedure Example4_BatchProcessingOptimization;
  end;

implementation

{ TOptimizationExample }

constructor TOptimizationExample.Create;
begin
  inherited Create;
  FThreadPdCal := TThreadPdCal.Create;
  FTestManager := TThreadPdCalTestManager.Create;
end;

destructor TOptimizationExample.Destroy;
begin
  FThreadPdCal.Free;
  FTestManager.Free;
  inherited Destroy;
end;

{ 示例1: 基本优化使用 }
procedure TOptimizationExample.Example1_BasicOptimization;
var
  Data1, Data2, Data3, DataKc, DataAll: TStringList;
  i: Integer;
begin
  // 创建测试数据
  Data1 := TStringList.Create;
  Data2 := TStringList.Create;
  Data3 := TStringList.Create;
  DataKc := TStringList.Create;
  DataAll := TStringList.Create;
  
  try
    // 填充测试数据 - 模拟真实的生产订单数据
    for i := 0 to 12 do
    begin
      Data1.Add(IntToStr(100 + i * 50)); // 各尺码数量
      Data2.Add('0');
      Data3.Add('0');
    end;
    
    ShowMessage('开始使用优化方法计算生产排档...');
    
    // 使用优化版本的Px方法
    FThreadPdCal.PxOptimized('10', '100', '1', '1', Data1, Data2, Data3, DataKc, DataAll);
    
    ShowMessage('优化方法计算完成！' + #13#10 + 
                '相比原始方法，优化版本具有以下优势：' + #13#10 +
                '1. 更快的排序算法（快速排序 vs 冒泡排序）' + #13#10 +
                '2. 字符串转换缓存，避免重复转换' + #13#10 +
                '3. 对象池管理，减少内存分配开销' + #13#10 +
                '4. 优化的循环逻辑，减少不必要的计算');
    
  finally
    Data1.Free;
    Data2.Free;
    Data3.Free;
    DataKc.Free;
    DataAll.Free;
  end;
end;

{ 示例2: 性能对比测试 }
procedure TOptimizationExample.Example2_PerformanceComparison;
begin
  ShowMessage('开始运行性能对比测试...' + #13#10 +
              '测试将包括：' + #13#10 +
              '1. 基本性能测试' + #13#10 +
              '2. 排序算法性能测试' + #13#10 +
              '3. 字符串转换性能测试' + #13#10 +
              '4. 内存使用测试');
  
  // 运行各项性能测试
  FTestManager.RunBasicPerformanceTest;
  FTestManager.RunSortingPerformanceTest;
  FTestManager.RunStringConversionTest;
  FTestManager.RunMemoryUsageTest;
  
  // 保存测试结果
  FTestManager.SaveTestResults('performance_comparison_results.txt');
  
  ShowMessage('性能对比测试完成！' + #13#10 +
              '测试结果已保存到 performance_comparison_results.txt 文件中。' + #13#10 +
              '请查看该文件了解详细的性能提升数据。');
end;

{ 示例3: 在现有代码中集成优化 }
procedure TOptimizationExample.Example3_IntegrateInExistingCode;
var
  ExampleCode: TStringList;
begin
  ExampleCode := TStringList.Create;
  try
    ExampleCode.Add('// 在现有代码中集成优化的示例');
    ExampleCode.Add('');
    ExampleCode.Add('// 原始代码：');
    ExampleCode.Add('// ThreadPdCal.Px(js, cs, fz_b, fz_e, Data1, Data2, Data3, DataKc, DataAll);');
    ExampleCode.Add('');
    ExampleCode.Add('// 优化后的代码：');
    ExampleCode.Add('// ThreadPdCal.PxOptimized(js, cs, fz_b, fz_e, Data1, Data2, Data3, DataKc, DataAll);');
    ExampleCode.Add('');
    ExampleCode.Add('// 集成步骤：');
    ExampleCode.Add('// 1. 在测试环境中，将原始调用替换为优化调用');
    ExampleCode.Add('// 2. 运行完整的功能测试，确保结果正确性');
    ExampleCode.Add('// 3. 运行性能测试，验证性能提升');
    ExampleCode.Add('// 4. 在生产环境中逐步部署优化版本');
    ExampleCode.Add('');
    ExampleCode.Add('// 注意事项：');
    ExampleCode.Add('// - 保留原始方法作为备份');
    ExampleCode.Add('// - 可以通过配置开关在两个版本间切换');
    ExampleCode.Add('// - 监控优化版本的稳定性和性能表现');
    
    ExampleCode.SaveToFile('integration_example.txt');
    
    ShowMessage('集成示例代码已保存到 integration_example.txt 文件中。' + #13#10 +
                '该文件包含了如何在现有代码中安全集成优化的详细说明。');
    
  finally
    ExampleCode.Free;
  end;
end;

{ 示例4: 批量处理优化 }
procedure TOptimizationExample.Example4_BatchProcessingOptimization;
var
  BatchData: TStringList;
  Data1, Data2, Data3, DataKc, DataAll: TStringList;
  i, j: Integer;
  StartTime, EndTime: TDateTime;
  ProcessingTime: Double;
begin
  BatchData := TStringList.Create;
  Data1 := TStringList.Create;
  Data2 := TStringList.Create;
  Data3 := TStringList.Create;
  DataKc := TStringList.Create;
  DataAll := TStringList.Create;
  
  try
    ShowMessage('开始批量处理优化示例...' + #13#10 +
                '将处理100个模拟订单，展示批量处理的性能优势。');
    
    StartTime := Now;
    
    // 模拟批量处理100个订单
    for i := 1 to 100 do
    begin
      // 清空之前的数据
      Data1.Clear;
      Data2.Clear;
      Data3.Clear;
      DataKc.Clear;
      DataAll.Clear;
      
      // 生成模拟订单数据
      for j := 0 to 12 do
      begin
        Data1.Add(IntToStr(50 + Random(200))); // 随机数量
        Data2.Add('0');
        Data3.Add('0');
      end;
      
      // 使用优化方法处理
      FThreadPdCal.PxOptimized(
        IntToStr(5 + Random(10)),  // 随机件数
        IntToStr(25 + Random(8) * 25), // 随机层数
        '1', '1', 
        Data1, Data2, Data3, DataKc, DataAll
      );
      
      // 每处理10个订单显示一次进度
      if i mod 10 = 0 then
      begin
        Application.ProcessMessages; // 保持界面响应
      end;
    end;
    
    EndTime := Now;
    ProcessingTime := (EndTime - StartTime) * 24 * 60 * 60; // 转换为秒
    
    BatchData.Add('=== 批量处理优化结果 ===');
    BatchData.Add('处理订单数量: 100');
    BatchData.Add('总处理时间: ' + FormatFloat('0.00', ProcessingTime) + ' 秒');
    BatchData.Add('平均每单处理时间: ' + FormatFloat('0.00', ProcessingTime / 100) + ' 秒');
    BatchData.Add('');
    BatchData.Add('优化效果：');
    BatchData.Add('- 字符串缓存减少了重复转换开销');
    BatchData.Add('- 对象池减少了内存分配次数');
    BatchData.Add('- 快速排序提升了排序效率');
    BatchData.Add('- 优化的算法逻辑减少了不必要的计算');
    
    BatchData.SaveToFile('batch_processing_results.txt');
    
    ShowMessage('批量处理完成！' + #13#10 +
                '处理了100个订单，总耗时: ' + FormatFloat('0.00', ProcessingTime) + ' 秒' + #13#10 +
                '详细结果已保存到 batch_processing_results.txt 文件中。');
    
  finally
    BatchData.Free;
    Data1.Free;
    Data2.Free;
    Data3.Free;
    DataKc.Free;
    DataAll.Free;
  end;
end;

end.
