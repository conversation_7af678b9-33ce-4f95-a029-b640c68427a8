unit Cc_LoadingTabFrm;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants,
  System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, RxAnimate, RxGIFCtrl, RxGIF,
  Vcl.ExtCtrls, Vcl.Imaging.jpeg, Vcl.StdCtrls, RzLabel, Vcl.Imaging.pngimage;

type
  TLoadingTabForm = class(TForm)
    Image1: TImage;
    RzLabel1: TRzLabel;
    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
  end;

var
  LoadingTabForm: TLoadingTabForm;

implementation

{$R *.dfm}

procedure TLoadingTabForm.FormCreate(Sender: TObject);
begin
  LoadingTabForm.left := (screen.width - LoadingTabForm.width) div 2;
  LoadingTabForm.top := (screen.height - LoadingTabForm.height) div 2;
end;

procedure TLoadingTabForm.FormShow(Sender: TObject);
begin
  // self.RxGIFAnimator1.Animate := true;
end;

end.
