unit Cc_LoadingSmallFrm;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants,
  System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, RxAnimate, RxGIFCtrl, RxGIF,
  Vcl.ExtCtrls, Vcl.Imaging.jpeg, Vcl.StdCtrls, RzLabel, Vcl.Imaging.pngimage;

type
  TLoadingSmallForm = class(TForm)
    Image1: TImage;
    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
  end;

var
  LoadingSmallForm: TLoadingSmallForm;

implementation

{$R *.dfm}

procedure TLoadingSmallForm.FormCreate(Sender: TObject);
begin
  LoadingSmallForm.left := (screen.width - LoadingSmallForm.width) div 2;
  LoadingSmallForm.top := (screen.height - LoadingSmallForm.height) div 2;
end;

procedure TLoadingSmallForm.FormShow(Sender: TObject);
begin
  // self.RxGIFAnimator1.Animate := true;
end;

end.
