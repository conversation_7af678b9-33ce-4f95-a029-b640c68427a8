unit CcMaterialDetail;

interface

uses
  Classes;

type
  TCcMaterialDetail = class
  private

    FMaterialDetailid: Integer;
    FMaterialFzid: Integer;
    FMaterialid: Integer;

    FDdid: Integer;
    FDdh: string;
    FKh: string;
    FXdrq: string;
    FChrq: string;
    FYwpm: string;
    FZwpm: string;
    FKsbm: string;
    FSjh: string;
    FDds: Integer;
    FLb: string;
    FMl: string;
    FKs: string;
    FKz: string;
    FYs: string;
    FYs_A: string;
    FYs_B: string;

    FShenhe: string;

    FCHXH: Integer;
    FFZTYPENUM: Integer;
    FFZ: Integer;

    FPDYS: string;
    FPDTGH: string;
    FPDML: string;
    FPDCK: string;
    FPDMF: string;
    FDJYJL: double;

    FPdnum: String;
    FPdnum1: Integer;
    FPdnum2: Integer;
    FCmtypenum: Integer;
    FCm_sum: Integer;
    FCMJYL_SUM: double;
    FCm_1: Integer;
    FCm_2: Integer;
    FCm_3: Integer;
    FCm_4: Integer;
    FCm_5: Integer;
    FCm_6: Integer;
    FCm_7: Integer;
    FCm_8: Integer;
    FCm_9: Integer;
    FCm_10: Integer;
    FCm_11: Integer;
    FCm_12: Integer;
    FCm_13: Integer;
    FCm_14: Integer;
    FCm_15: Integer;
    FCj_sum: Integer;
    FCjbc: double;
    FCj_1: Integer;
    FCj_2: Integer;
    FCj_3: Integer;
    FCj_4: Integer;
    FCj_5: Integer;
    FCj_6: Integer;
    FCj_7: Integer;
    FCj_8: Integer;
    FCj_9: Integer;
    FCj_10: Integer;
    FCj_11: Integer;
    FCj_12: Integer;
    FCj_13: Integer;
    FCj_14: Integer;
    FCj_15: Integer;

    FMarketTime: string;
    FMarketUser: string;
    FState: string;

    FItem_X_X_1: double;
    FItem_X_X_2: double;
    FItem_X_X_3: double;
    FItem_X_X_4: double;
    FItem_X_X_5: double;

    FItem_X_X_9: double;
    FItem_X_X_10: double;
    FItem_X_X_11: double;
    FItem_X_X_12: double;
    FItem_X_X_13: double;
    FItem_X_X_14: double;
    FItem_X_X_15: double;
    FItem_X_X_16: double;
    FItem_X_X_17: double;
    FItem_X_X_18: double;
    FItem_X_X_19: double;
    FItem_X_X_20: double;

  public
    property MaterialDetailid: Integer read FMaterialDetailid
      write FMaterialDetailid;
    property MaterialFzid: Integer read FMaterialFzid write FMaterialFzid;
    property Materialid: Integer read FMaterialid write FMaterialid;
    property Ddid: Integer read FDdid write FDdid;
    property Ddh: string read FDdh write FDdh;
    property Kh: string read FKh write FKh;
    property Xdrq: string read FXdrq write FXdrq;
    property Chrq: string read FChrq write FChrq;
    property Ywpm: string read FYwpm write FYwpm;
    property Zwpm: string read FZwpm write FZwpm;
    property Ksbm: string read FKsbm write FKsbm;
    property Sjh: string read FSjh write FSjh;
    property Dds: Integer read FDds write FDds;
    property Lb: string read FLb write FLb;
    property Ml: string read FMl write FMl;
    property Ks: string read FKs write FKs;
    property Kz: string read FKz write FKz;
    property Ys: string read FYs write FYs;

    property Ys_A: string read FYs_A write FYs_A;
    property Ys_B: string read FYs_B write FYs_B;

    property Shenhe: string read FShenhe write FShenhe;
    property PDYS: string read FPDYS write FPDYS;
    property PDTGH: string read FPDTGH write FPDTGH;
    property PDML: string read FPDML write FPDML;
    property PDCK: string read FPDCK write FPDCK;
    property PDMF: string read FPDMF write FPDMF;
    property DJYJL: double read FDJYJL write FDJYJL;

    property CHXH: Integer read FCHXH write FCHXH;
    property FZTYPENUM: Integer read FFZTYPENUM write FFZTYPENUM;
    property FZ: Integer read FFZ write FFZ;

    property Pdnum: string read FPdnum write FPdnum;
    property Pdnum1: Integer read FPdnum1 write FPdnum1;
    property Pdnum2: Integer read FPdnum2 write FPdnum2;
    property Cmtypenum: Integer read FCmtypenum write FCmtypenum;

    property Cm_sum: Integer read FCm_sum write FCm_sum;
    property CMJYL_SUM: double read FCMJYL_SUM write FCMJYL_SUM;
    property Cm_1: Integer read FCm_1 write FCm_1;
    property Cm_2: Integer read FCm_2 write FCm_2;
    property Cm_3: Integer read FCm_3 write FCm_3;
    property Cm_4: Integer read FCm_4 write FCm_4;
    property Cm_5: Integer read FCm_5 write FCm_5;
    property Cm_6: Integer read FCm_6 write FCm_6;
    property Cm_7: Integer read FCm_7 write FCm_7;
    property Cm_8: Integer read FCm_8 write FCm_8;
    property Cm_9: Integer read FCm_9 write FCm_9;
    property Cm_10: Integer read FCm_10 write FCm_10;
    property Cm_11: Integer read FCm_11 write FCm_11;
    property Cm_12: Integer read FCm_12 write FCm_12;
    property Cm_13: Integer read FCm_13 write FCm_13;
    property Cm_14: Integer read FCm_14 write FCm_14;
    property Cm_15: Integer read FCm_15 write FCm_15;

    property Cj_sum: Integer read FCj_sum write FCj_sum;
    property Cjbc: double read FCjbc write FCjbc;
    property Cj_1: Integer read FCj_1 write FCj_1;
    property Cj_2: Integer read FCj_2 write FCj_2;
    property Cj_3: Integer read FCj_3 write FCj_3;
    property Cj_4: Integer read FCj_4 write FCj_4;
    property Cj_5: Integer read FCj_5 write FCj_5;
    property Cj_6: Integer read FCj_6 write FCj_6;
    property Cj_7: Integer read FCj_7 write FCj_7;
    property Cj_8: Integer read FCj_8 write FCj_8;
    property Cj_9: Integer read FCj_9 write FCj_9;
    property Cj_10: Integer read FCj_10 write FCj_10;
    property Cj_11: Integer read FCj_11 write FCj_11;
    property Cj_12: Integer read FCj_12 write FCj_12;
    property Cj_13: Integer read FCj_13 write FCj_13;
    property Cj_14: Integer read FCj_14 write FCj_14;
    property Cj_15: Integer read FCj_15 write FCj_15;

    property Item_X_X_1: double read FItem_X_X_1 write FItem_X_X_1;
    property Item_X_X_2: double read FItem_X_X_2 write FItem_X_X_2;
    property Item_X_X_3: double read FItem_X_X_3 write FItem_X_X_3;
    property Item_X_X_4: double read FItem_X_X_4 write FItem_X_X_4;
    property Item_X_X_5: double read FItem_X_X_5 write FItem_X_X_5;

    property Item_X_X_9: double read FItem_X_X_9 write FItem_X_X_9;
    property Item_X_X_10: double read FItem_X_X_10 write FItem_X_X_10;
    property Item_X_X_11: double read FItem_X_X_11 write FItem_X_X_11;
    property Item_X_X_12: double read FItem_X_X_12 write FItem_X_X_12;
    property Item_X_X_13: double read FItem_X_X_13 write FItem_X_X_13;
    property Item_X_X_14: double read FItem_X_X_14 write FItem_X_X_14;
    property Item_X_X_15: double read FItem_X_X_15 write FItem_X_X_15;
    property Item_X_X_16: double read FItem_X_X_16 write FItem_X_X_16;
    property Item_X_X_17: double read FItem_X_X_17 write FItem_X_X_17;
    property Item_X_X_18: double read FItem_X_X_18 write FItem_X_X_18;
    property Item_X_X_19: double read FItem_X_X_19 write FItem_X_X_19;
    property Item_X_X_20: double read FItem_X_X_20 write FItem_X_X_20;

    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;
  end;

implementation

end.
